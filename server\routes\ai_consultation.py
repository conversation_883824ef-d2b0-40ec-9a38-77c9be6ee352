"""
AI Consultation routes
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from models.database import get_db
from models.medical_record import AIConsultation
from models.ai_engine import MedicalAIEngine
from schemas.ai_consultation import AIConsultationRequest, AIConsultationResponse, AIConsultationHistory
from services.auth import get_current_user
from models.user import User

router = APIRouter()

# Initialize AI engine
ai_engine = MedicalAIEngine()

@router.post("/consult", response_model=AIConsultationResponse)
async def ai_consultation(
    request: AIConsultationRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get AI-powered medical consultation based on symptoms
    """
    try:
        # Analyze symptoms using AI engine
        result = ai_engine.analyze_symptoms(
            symptoms=request.symptoms,
            age=request.age,
            gender=request.gender
        )
        
        # Save consultation to database
        consultation = AIConsultation(
            patient_id=current_user.id,
            age=request.age,
            gender=request.gender,
            symptoms=request.symptoms,
            advice=result['advice'],
            urgency=result['urgency'],
            suggested_action=result['suggested_action'],
            confidence_score=result['confidence_score']
        )
        
        db.add(consultation)
        db.commit()
        db.refresh(consultation)
        
        return AIConsultationResponse(
            advice=result['advice'],
            urgency=result['urgency'],
            suggested_action=result['suggested_action'],
            confidence_score=result['confidence_score']
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing consultation: {str(e)}"
        )

@router.get("/history", response_model=List[AIConsultationHistory])
async def get_consultation_history(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    limit: int = 10
):
    """
    Get user's AI consultation history
    """
    consultations = db.query(AIConsultation)\
        .filter(AIConsultation.patient_id == current_user.id)\
        .order_by(AIConsultation.created_at.desc())\
        .limit(limit)\
        .all()
    
    return consultations

@router.get("/consult/demo", response_model=AIConsultationResponse)
async def demo_consultation():
    """
    Demo endpoint for testing AI consultation without authentication
    """
    demo_request = AIConsultationRequest(
        age=28,
        gender="male",
        symptoms=["headache", "nausea", "fatigue"]
    )
    
    result = ai_engine.analyze_symptoms(
        symptoms=demo_request.symptoms,
        age=demo_request.age,
        gender=demo_request.gender
    )
    
    return AIConsultationResponse(
        advice=result['advice'],
        urgency=result['urgency'],
        suggested_action=result['suggested_action'],
        confidence_score=result['confidence_score']
    )
