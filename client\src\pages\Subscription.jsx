import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import toast from 'react-hot-toast'

const Subscription = () => {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)

  const plans = [
    {
      name: 'Free',
      price: '$0',
      period: 'forever',
      features: [
        'Basic AI consultations',
        'Limited appointments',
        'Basic health tracking',
        'Email support'
      ],
      current: user?.subscription_plan === 'free',
      buttonText: 'Current Plan',
      disabled: true
    },
    {
      name: 'Basic',
      price: '$9.99',
      period: 'month',
      features: [
        'Unlimited AI consultations',
        'Video appointments',
        'Advanced health tracking',
        'Priority support',
        'Medical records storage'
      ],
      current: user?.subscription_plan === 'basic',
      buttonText: user?.subscription_plan === 'basic' ? 'Current Plan' : 'Upgrade to Basic',
      disabled: user?.subscription_plan === 'basic'
    },
    {
      name: 'Premium',
      price: '$19.99',
      period: 'month',
      features: [
        'Everything in Basic',
        'Specialist consultations',
        'Family account (up to 4 members)',
        '24/7 emergency support',
        'Prescription management',
        'Health insights & analytics'
      ],
      current: user?.subscription_plan === 'premium',
      buttonText: user?.subscription_plan === 'premium' ? 'Current Plan' : 'Upgrade to Premium',
      disabled: user?.subscription_plan === 'premium'
    }
  ]

  const handleUpgrade = async (planName) => {
    setLoading(true)
    
    try {
      // In a real implementation, this would integrate with Stripe
      toast.success(`Upgrade to ${planName} plan initiated! (Demo mode)`)
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast.success('Payment processed successfully!')
    } catch (error) {
      toast.error('Payment failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Select the perfect plan for your healthcare needs. Upgrade or downgrade at any time.
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8">
        {plans.map((plan, index) => (
          <div
            key={index}
            className={`bg-white rounded-lg shadow-lg p-8 relative ${
              plan.current ? 'ring-2 ring-primary-500' : ''
            }`}
          >
            {plan.current && (
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <span className="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                  Current Plan
                </span>
              </div>
            )}

            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
              <div className="mb-4">
                <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                <span className="text-gray-600">/{plan.period}</span>
              </div>
            </div>

            <ul className="space-y-4 mb-8">
              {plan.features.map((feature, featureIndex) => (
                <li key={featureIndex} className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-gray-700">{feature}</span>
                </li>
              ))}
            </ul>

            <button
              onClick={() => handleUpgrade(plan.name)}
              disabled={plan.disabled || loading}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                plan.disabled
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-primary-600 hover:bg-primary-700 text-white'
              }`}
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Processing...
                </div>
              ) : (
                plan.buttonText
              )}
            </button>
          </div>
        ))}
      </div>

      {/* Billing Information */}
      <div className="mt-12 bg-gray-50 rounded-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Billing Information</h2>
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Current Subscription</h3>
            <p className="text-gray-600 mb-4">
              You are currently on the <span className="font-medium capitalize">{user?.subscription_plan || 'Free'}</span> plan.
            </p>
            
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Payment Method</h3>
            <p className="text-gray-600">
              No payment method on file. Add a payment method to upgrade your plan.
            </p>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Next Billing Date</h3>
            <p className="text-gray-600 mb-4">
              {user?.subscription_plan === 'free' ? 'No billing required' : 'Not available in demo'}
            </p>
            
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Billing History</h3>
            <p className="text-gray-600">
              No billing history available.
            </p>
          </div>
        </div>
      </div>

      {/* FAQ */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Frequently Asked Questions</h2>
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Can I change my plan anytime?</h3>
            <p className="text-gray-600">
              Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
            </p>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Is there a free trial?</h3>
            <p className="text-gray-600">
              All new users start with our Free plan. You can upgrade to paid plans when you need additional features.
            </p>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">What payment methods do you accept?</h3>
            <p className="text-gray-600">
              We accept all major credit cards, debit cards, and digital wallets through our secure payment processor.
            </p>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Can I cancel anytime?</h3>
            <p className="text-gray-600">
              Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your billing period.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Subscription
