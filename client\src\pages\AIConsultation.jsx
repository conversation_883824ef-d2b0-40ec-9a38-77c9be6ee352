import React, { useState } from 'react'
import axios from 'axios'
import toast from 'react-hot-toast'

const AIConsultation = () => {
  const [formData, setFormData] = useState({
    age: '',
    gender: '',
    symptoms: ''
  })
  const [result, setResult] = useState(null)
  const [loading, setLoading] = useState(false)

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Convert symptoms string to array
      const symptomsArray = formData.symptoms
        .split(',')
        .map(s => s.trim())
        .filter(s => s.length > 0)

      const requestData = {
        age: parseInt(formData.age),
        gender: formData.gender,
        symptoms: symptomsArray
      }

      const response = await axios.post('/api/v1/ai/consult', requestData)
      setResult(response.data)
      toast.success('Consultation completed!')
    } catch (error) {
      const message = error.response?.data?.detail || 'Consultation failed'
      toast.error(message)
    } finally {
      setLoading(false)
    }
  }

  const getUrgencyColor = (urgency) => {
    switch (urgency?.toLowerCase()) {
      case 'high':
        return 'text-red-600 bg-red-100'
      case 'medium':
        return 'text-yellow-600 bg-yellow-100'
      case 'low':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">AI Medical Consultation</h1>
        <p className="text-gray-600">
          Get instant medical advice based on your symptoms. This is not a replacement for professional medical care.
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Consultation Form */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Tell us about your symptoms</h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="age" className="block text-sm font-medium text-gray-700 mb-1">
                Age
              </label>
              <input
                type="number"
                id="age"
                name="age"
                min="1"
                max="150"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter your age"
                value={formData.age}
                onChange={handleChange}
              />
            </div>

            <div>
              <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-1">
                Gender
              </label>
              <select
                id="gender"
                name="gender"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                value={formData.gender}
                onChange={handleChange}
              >
                <option value="">Select gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div>
              <label htmlFor="symptoms" className="block text-sm font-medium text-gray-700 mb-1">
                Symptoms
              </label>
              <textarea
                id="symptoms"
                name="symptoms"
                rows="4"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Describe your symptoms separated by commas (e.g., headache, nausea, fever)"
                value={formData.symptoms}
                onChange={handleChange}
              />
              <p className="text-sm text-gray-500 mt-1">
                Separate multiple symptoms with commas
              </p>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-md font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Analyzing...
                </div>
              ) : (
                'Get AI Consultation'
              )}
            </button>
          </form>

          {/* Disclaimer */}
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Important Disclaimer</h3>
                <p className="text-sm text-yellow-700 mt-1">
                  This AI consultation is for informational purposes only and should not replace professional medical advice. 
                  Always consult with a qualified healthcare provider for serious symptoms.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Results */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Consultation Results</h2>
          
          {result ? (
            <div className="space-y-4">
              {/* Urgency Level */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Urgency Level</h3>
                <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getUrgencyColor(result.urgency)}`}>
                  {result.urgency}
                </span>
              </div>

              {/* Medical Advice */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Medical Advice</h3>
                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <p className="text-blue-800">{result.advice}</p>
                </div>
              </div>

              {/* Suggested Action */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Suggested Action</h3>
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <p className="text-green-800">{result.suggested_action}</p>
                </div>
              </div>

              {/* Confidence Score */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Confidence Score</h3>
                <span className="inline-flex px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                  {result.confidence_score}
                </span>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4 pt-4">
                <button
                  onClick={() => setResult(null)}
                  className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md font-medium transition-colors"
                >
                  New Consultation
                </button>
                <button className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-md font-medium transition-colors">
                  Book Appointment
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No consultation yet</h3>
              <p className="mt-1 text-sm text-gray-500">
                Fill out the form to get your AI-powered medical consultation.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default AIConsultation
