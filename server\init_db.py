"""
Database initialization script
"""

from sqlalchemy import create_engine
from models.database import Base, DATABASE_URL
from models.user import User
from models.appointment import Appointment
from models.medical_record import MedicalRecord, AIConsultation

def init_database():
    """Initialize the database with all tables"""
    engine = create_engine(DATABASE_URL)
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    print("Database tables created successfully!")

if __name__ == "__main__":
    init_database()
