{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@daily-co/daily-js": "^0.80.0", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@tailwindcss/vite": "^4.1.11", "axios": "^1.10.0", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zod": "^3.25.69"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^7.0.0"}}