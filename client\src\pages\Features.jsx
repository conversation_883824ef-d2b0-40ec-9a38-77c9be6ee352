import React from 'react'
import { motion } from 'framer-motion'
import { 
  Bot, Video, Shield, Clock, Users, Heart, 
  Smartphone, Globe, Award, Zap, Brain, Lock 
} from 'lucide-react'
import { useTheme } from '../contexts/ThemeContext'
import { cn } from '../utils/cn'
import { fadeInUp, staggerContainer, staggerItem, hoverLift } from '../utils/animations'

const Features = () => {
  const { isDark } = useTheme()

  const features = [
    {
      icon: Bot,
      title: 'الذكاء الاصطناعي المتقدم',
      titleEn: 'Advanced AI',
      description: 'نظام ذكي متطور لتحليل الأعراض وتقديم التشخيص الأولي بدقة عالية',
      descriptionEn: 'Advanced AI system for symptom analysis and accurate preliminary diagnosis',
      color: 'from-blue-500 to-cyan-500',
      features: ['تحليل الأعراض', 'التشخيص الذكي', 'النصائح الطبية', 'التعلم المستمر']
    },
    {
      icon: Video,
      title: 'الاستشارات المرئية',
      titleEn: 'Video Consultations',
      description: 'تواصل مع الأطباء المختصين عبر مكالمات فيديو آمنة وعالية الجودة',
      descriptionEn: 'Connect with specialist doctors through secure, high-quality video calls',
      color: 'from-green-500 to-emerald-500',
      features: ['مكالمات HD', 'حجز فوري', 'أطباء معتمدين', 'سجل طبي']
    },
    {
      icon: Shield,
      title: 'الأمان والخصوصية',
      titleEn: 'Security & Privacy',
      description: 'حماية متقدمة لبياناتك الطبية مع التشفير الكامل والامتثال للمعايير الدولية',
      descriptionEn: 'Advanced protection for your medical data with full encryption and international compliance',
      color: 'from-purple-500 to-pink-500',
      features: ['تشفير كامل', 'HIPAA متوافق', 'خصوصية تامة', 'نسخ احتياطية']
    },
    {
      icon: Clock,
      title: 'متاح 24/7',
      titleEn: '24/7 Available',
      description: 'خدمة طبية متاحة على مدار الساعة لضمان حصولك على المساعدة عند الحاجة',
      descriptionEn: 'Medical service available 24/7 to ensure you get help when you need it',
      color: 'from-orange-500 to-red-500',
      features: ['خدمة مستمرة', 'استجابة سريعة', 'طوارئ', 'دعم فوري']
    },
    {
      icon: Users,
      title: 'فريق طبي متخصص',
      titleEn: 'Specialized Medical Team',
      description: 'شبكة من الأطباء المعتمدين والمختصين في مختلف المجالات الطبية',
      descriptionEn: 'Network of certified doctors and specialists in various medical fields',
      color: 'from-indigo-500 to-blue-500',
      features: ['أطباء معتمدين', 'تخصصات متنوعة', 'خبرة عالية', 'تقييمات المرضى']
    },
    {
      icon: Smartphone,
      title: 'سهولة الاستخدام',
      titleEn: 'Easy to Use',
      description: 'واجهة بسيطة وسهلة الاستخدام تناسب جميع الأعمار والمستويات التقنية',
      descriptionEn: 'Simple and easy-to-use interface suitable for all ages and technical levels',
      color: 'from-teal-500 to-green-500',
      features: ['واجهة بديهية', 'تصميم متجاوب', 'دعم متعدد اللغات', 'إرشادات واضحة']
    }
  ]

  const stats = [
    { number: '50K+', label: 'مستخدم نشط', labelEn: 'Active Users' },
    { number: '1000+', label: 'طبيب معتمد', labelEn: 'Certified Doctors' },
    { number: '99.9%', label: 'وقت التشغيل', labelEn: 'Uptime' },
    { number: '4.9/5', label: 'تقييم المستخدمين', labelEn: 'User Rating' }
  ]

  return (
    <motion.div
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      className="max-w-7xl mx-auto"
    >
      {/* Header */}
      <motion.div variants={staggerItem} className="text-center mb-16">
        <motion.h1 
          className={cn(
            "text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent",
            isDark && "from-blue-400 via-purple-400 to-pink-400"
          )}
        >
          مميزات Si7ati
        </motion.h1>
        <motion.p 
          className={cn(
            "text-xl max-w-3xl mx-auto leading-relaxed",
            isDark ? "text-gray-300" : "text-gray-600"
          )}
        >
          اكتشف كيف تعيد Si7ati تعريف الرعاية الصحية الرقمية من خلال التكنولوجيا المتقدمة والخدمات الطبية الشاملة
        </motion.p>
      </motion.div>

      {/* Stats */}
      <motion.div 
        variants={staggerItem}
        className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20"
      >
        {stats.map((stat, index) => (
          <motion.div
            key={index}
            variants={hoverLift}
            whileHover="whileHover"
            className={cn(
              "text-center p-6 rounded-2xl",
              isDark ? "bg-gray-800/50 border border-gray-700" : "bg-white/50 border border-gray-200"
            )}
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: index * 0.1, type: "spring" }}
              className={cn(
                "text-3xl font-bold mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",
                isDark && "from-blue-400 to-purple-400"
              )}
            >
              {stat.number}
            </motion.div>
            <p className={cn(
              "text-sm font-medium",
              isDark ? "text-gray-300" : "text-gray-600"
            )}>
              {stat.label}
            </p>
            <p className={cn(
              "text-xs mt-1",
              isDark ? "text-gray-400" : "text-gray-500"
            )}>
              {stat.labelEn}
            </p>
          </motion.div>
        ))}
      </motion.div>

      {/* Features Grid */}
      <motion.div 
        variants={staggerContainer}
        className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20"
      >
        {features.map((feature, index) => (
          <motion.div
            key={index}
            variants={staggerItem}
            whileHover={{ y: -10, scale: 1.02 }}
            className={cn(
              "p-8 rounded-3xl border backdrop-blur-sm transition-all duration-300",
              isDark 
                ? "bg-gray-800/50 border-gray-700 hover:border-gray-600" 
                : "bg-white/50 border-gray-200 hover:border-gray-300"
            )}
          >
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: index * 0.1, type: "spring" }}
              className={cn(
                "w-16 h-16 rounded-2xl flex items-center justify-center mb-6",
                `bg-gradient-to-r ${feature.color}`
              )}
            >
              <feature.icon className="w-8 h-8 text-white" />
            </motion.div>

            <h3 className={cn(
              "text-xl font-bold mb-2",
              isDark ? "text-white" : "text-gray-900"
            )}>
              {feature.title}
            </h3>
            
            <p className={cn(
              "text-sm mb-1 opacity-70",
              isDark ? "text-gray-400" : "text-gray-500"
            )}>
              {feature.titleEn}
            </p>

            <p className={cn(
              "text-sm leading-relaxed mb-6",
              isDark ? "text-gray-300" : "text-gray-600"
            )}>
              {feature.description}
            </p>

            <div className="space-y-2">
              {feature.features.map((item, itemIndex) => (
                <motion.div
                  key={itemIndex}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: (index * 0.1) + (itemIndex * 0.05) }}
                  className="flex items-center text-sm"
                >
                  <div className={cn(
                    "w-2 h-2 rounded-full mr-3",
                    `bg-gradient-to-r ${feature.color}`
                  )} />
                  <span className={cn(
                    isDark ? "text-gray-300" : "text-gray-600"
                  )}>
                    {item}
                  </span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Technology Section */}
      <motion.div 
        variants={staggerItem}
        className={cn(
          "p-12 rounded-3xl mb-20",
          isDark 
            ? "bg-gradient-to-r from-gray-800 to-gray-900 border border-gray-700" 
            : "bg-gradient-to-r from-blue-50 to-purple-50 border border-gray-200"
        )}
      >
        <div className="text-center mb-12">
          <motion.h2 
            className={cn(
              "text-3xl font-bold mb-4",
              isDark ? "text-white" : "text-gray-900"
            )}
          >
            التكنولوجيا المتقدمة
          </motion.h2>
          <p className={cn(
            "text-lg",
            isDark ? "text-gray-300" : "text-gray-600"
          )}>
            نستخدم أحدث التقنيات لضمان أفضل تجربة طبية رقمية
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {[
            { icon: Brain, title: 'الذكاء الاصطناعي', desc: 'خوارزميات متطورة للتشخيص' },
            { icon: Lock, title: 'الأمان المتقدم', desc: 'تشفير من الدرجة العسكرية' },
            { icon: Zap, title: 'الأداء السريع', desc: 'استجابة فورية وموثوقية عالية' }
          ].map((tech, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2 }}
              className="text-center"
            >
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                className={cn(
                  "w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-4",
                  isDark 
                    ? "bg-gradient-to-r from-blue-600 to-purple-600" 
                    : "bg-gradient-to-r from-blue-500 to-purple-500"
                )}
              >
                <tech.icon className="w-10 h-10 text-white" />
              </motion.div>
              <h3 className={cn(
                "text-lg font-semibold mb-2",
                isDark ? "text-white" : "text-gray-900"
              )}>
                {tech.title}
              </h3>
              <p className={cn(
                "text-sm",
                isDark ? "text-gray-300" : "text-gray-600"
              )}>
                {tech.desc}
              </p>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* CTA Section */}
      <motion.div 
        variants={staggerItem}
        className="text-center"
      >
        <motion.div
          whileHover={{ scale: 1.02 }}
          className={cn(
            "p-12 rounded-3xl bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white"
          )}
        >
          <Award className="w-16 h-16 mx-auto mb-6" />
          <h2 className="text-3xl font-bold mb-4">
            جاهز لتجربة المستقبل؟
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            انضم إلى آلاف المستخدمين الذين يثقون في Si7ati لرعايتهم الصحية
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg"
              onClick={() => window.location.href = '/register'}
            >
              ابدأ مجاناً
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold text-lg"
              onClick={() => window.location.href = '/demo'}
            >
              جرب التطبيق
            </motion.button>
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  )
}

export default Features
