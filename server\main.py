"""
HealthSync FastAPI Application
Main entry point for the AI-powered e-consultation SaaS platform
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Import routers
from routes.auth import router as auth_router
from routes.users import router as users_router
from routes.appointments import router as appointments_router
from routes.ai_consultation import router as ai_router
from routes.payments import router as payments_router

# Create FastAPI app
app = FastAPI(
    title="HealthSync API",
    description="AI-Powered E-Consultation SaaS Platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(users_router, prefix="/api/v1/users", tags=["Users"])
app.include_router(appointments_router, prefix="/api/v1/appointments", tags=["Appointments"])
app.include_router(ai_router, prefix="/api/v1/ai", tags=["AI Consultation"])
app.include_router(payments_router, prefix="/api/v1/payments", tags=["Payments"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to HealthSync API",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "HealthSync API"}

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Global HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail, "status_code": exc.status_code}
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=True
    )
