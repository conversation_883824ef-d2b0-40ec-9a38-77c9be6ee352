import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Globe, ChevronDown } from 'lucide-react'
import { useLanguage } from '../contexts/LanguageContext'
import { useTheme } from '../contexts/ThemeContext'
import { cn } from '../utils/cn'

const LanguageSwitcher = () => {
  const [isOpen, setIsOpen] = useState(false)
  const { currentLanguage, changeLanguage, languages, isRTL } = useLanguage()
  const { isDark } = useTheme()
  const dropdownRef = useRef(null)

  const currentLang = languages.find(lang => lang.code === currentLanguage)

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLanguageChange = (langCode) => {
    changeLanguage(langCode)
    setIsOpen(false)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className={cn(
          "flex items-center space-x-2 px-3 py-2 rounded-lg border transition-all duration-200",
          isRTL && "space-x-reverse",
          isDark 
            ? "bg-gray-800 border-gray-700 text-gray-300 hover:bg-gray-700 hover:border-gray-600" 
            : "bg-white border-gray-200 text-gray-700 hover:bg-gray-50 hover:border-gray-300"
        )}
      >
        <Globe className="w-4 h-4" />
        <span className="text-lg">{currentLang?.flag}</span>
        <span className="text-sm font-medium hidden sm:block">
          {currentLang?.name}
        </span>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronDown className="w-4 h-4" />
        </motion.div>
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "absolute top-full mt-2 w-48 rounded-xl border shadow-lg backdrop-blur-sm z-50",
              isRTL ? "right-0" : "left-0",
              isDark 
                ? "bg-gray-800/95 border-gray-700" 
                : "bg-white/95 border-gray-200"
            )}
          >
            <div className="py-2">
              {languages.map((language, index) => (
                <motion.button
                  key={language.code}
                  onClick={() => handleLanguageChange(language.code)}
                  initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className={cn(
                    "w-full flex items-center space-x-3 px-4 py-3 text-left transition-all duration-200",
                    isRTL && "space-x-reverse text-right",
                    currentLanguage === language.code
                      ? isDark
                        ? "bg-blue-900/50 text-blue-300"
                        : "bg-blue-50 text-blue-700"
                      : isDark
                        ? "text-gray-300 hover:bg-gray-700"
                        : "text-gray-700 hover:bg-gray-50"
                  )}
                >
                  <span className="text-xl">{language.flag}</span>
                  <div className="flex-1">
                    <div className={cn(
                      "font-medium",
                      currentLanguage === language.code && (isDark ? "text-blue-300" : "text-blue-700")
                    )}>
                      {language.name}
                    </div>
                    <div className={cn(
                      "text-xs opacity-70",
                      currentLanguage === language.code 
                        ? isDark ? "text-blue-400" : "text-blue-600"
                        : isDark ? "text-gray-400" : "text-gray-500"
                    )}>
                      {language.code.toUpperCase()}
                    </div>
                  </div>
                  {currentLanguage === language.code && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className={cn(
                        "w-2 h-2 rounded-full",
                        isDark ? "bg-blue-400" : "bg-blue-600"
                      )}
                    />
                  )}
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default LanguageSwitcher
