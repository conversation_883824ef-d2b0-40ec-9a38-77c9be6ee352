# HealthSync Deployment Guide

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Python 3.9+
- Git

### Local Development Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd healthsync-app
```

2. **Backend Setup**
```bash
cd server
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
# Edit .env with your configuration
python init_db.py
python main.py
```

3. **Frontend Setup**
```bash
cd client
npm install
cp .env.example .env
# Edit .env with your configuration
npm run dev
```

4. **Access the Application**
- Frontend: http://localhost:5173
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## 🌐 Production Deployment

### Backend Deployment (Railway/Render)

1. **Prepare for deployment**
```bash
cd server
pip freeze > requirements.txt
```

2. **Create Procfile**
```
web: uvicorn main:app --host 0.0.0.0 --port $PORT
```

3. **Environment Variables**
Set these in your deployment platform:
```
DATABASE_URL=postgresql://user:password@host:port/database
SECRET_KEY=your-production-secret-key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
FRONTEND_URL=https://your-frontend-domain.com
```

### Frontend Deployment (Vercel/Netlify)

1. **Build configuration**
```bash
cd client
npm run build
```

2. **Environment Variables**
Set these in your deployment platform:
```
VITE_API_URL=https://your-backend-domain.com
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
VITE_DAILY_API_KEY=your_daily_api_key
```

### Database Setup

**PostgreSQL (Production)**
```sql
CREATE DATABASE healthsync;
CREATE USER healthsync_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE healthsync TO healthsync_user;
```

## 🔐 Security Configuration

### SSL/HTTPS
- Enable HTTPS on both frontend and backend
- Use environment variables for sensitive data
- Implement CORS properly for production domains

### Database Security
- Use connection pooling
- Enable SSL for database connections
- Regular backups and encryption at rest

### API Security
- Rate limiting
- Input validation
- JWT token expiration
- Secure headers

## 📊 Monitoring & Analytics

### Health Checks
- Backend: `GET /health`
- Database connectivity
- External service availability

### Logging
- Application logs
- Error tracking (Sentry recommended)
- Performance monitoring

## 🔧 Configuration

### Required Environment Variables

**Backend (.env)**
```
DATABASE_URL=postgresql://...
SECRET_KEY=...
STRIPE_SECRET_KEY=...
SMTP_HOST=...
SMTP_USER=...
SMTP_PASSWORD=...
```

**Frontend (.env)**
```
VITE_API_URL=...
VITE_STRIPE_PUBLISHABLE_KEY=...
VITE_DAILY_API_KEY=...
```

## 🧪 Testing

### Backend Tests
```bash
cd server
pytest
```

### Frontend Tests
```bash
cd client
npm test
```

### API Testing
```bash
cd server
python test_api.py
```

## 📈 Scaling Considerations

### Backend Scaling
- Use load balancers
- Implement caching (Redis)
- Database read replicas
- Microservices architecture

### Frontend Scaling
- CDN for static assets
- Image optimization
- Code splitting
- Progressive Web App features

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy
on:
  push:
    branches: [main]
jobs:
  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Railway
        # Add deployment steps
  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Vercel
        # Add deployment steps
```

## 🆘 Troubleshooting

### Common Issues
1. **CORS errors**: Check FRONTEND_URL in backend .env
2. **Database connection**: Verify DATABASE_URL format
3. **Authentication issues**: Check JWT SECRET_KEY
4. **Payment failures**: Verify Stripe keys and webhooks

### Debug Commands
```bash
# Check backend logs
tail -f logs/app.log

# Test API endpoints
curl http://localhost:8000/health

# Check database connection
python -c "from models.database import engine; print(engine.execute('SELECT 1').scalar())"
```

## 📞 Support

For deployment issues:
1. Check the logs first
2. Verify environment variables
3. Test API endpoints individually
4. Check database connectivity
5. Review security settings
