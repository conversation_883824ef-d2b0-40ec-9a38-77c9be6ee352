import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import VideoCall from '../components/VideoCall'

const VideoConsultation = () => {
  const [roomUrl, setRoomUrl] = useState('')
  const [isInCall, setIsInCall] = useState(false)
  const navigate = useNavigate()

  const handleJoinCall = () => {
    if (roomUrl.trim()) {
      setIsInCall(true)
    }
  }

  const handleLeaveCall = () => {
    setIsInCall(false)
    setRoomUrl('')
    navigate('/dashboard')
  }

  const createDemoRoom = () => {
    // For demo purposes, create a sample room URL
    const demoRoomUrl = 'https://healthsync.daily.co/demo-room'
    setRoomUrl(demoRoomUrl)
  }

  if (isInCall) {
    return (
      <div className="fixed inset-0 bg-black z-50">
        <VideoCall roomUrl={roomUrl} onLeave={handleLeaveCall} />
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Video Consultation</h1>
        <p className="text-gray-600">
          Join a secure video call with your healthcare provider.
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="max-w-md mx-auto">
          <div className="mb-6">
            <label htmlFor="roomUrl" className="block text-sm font-medium text-gray-700 mb-2">
              Room URL
            </label>
            <input
              type="url"
              id="roomUrl"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Enter video call room URL"
              value={roomUrl}
              onChange={(e) => setRoomUrl(e.target.value)}
            />
          </div>

          <div className="space-y-4">
            <button
              onClick={handleJoinCall}
              disabled={!roomUrl.trim()}
              className="w-full bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-md font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Join Video Call
            </button>

            <button
              onClick={createDemoRoom}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md font-medium transition-colors"
            >
              Create Demo Room
            </button>
          </div>

          {/* Instructions */}
          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h3 className="text-sm font-medium text-blue-800 mb-2">Instructions:</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Make sure you have a stable internet connection</li>
              <li>• Allow camera and microphone permissions when prompted</li>
              <li>• Use headphones for better audio quality</li>
              <li>• Find a quiet, well-lit space for the consultation</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default VideoConsultation
