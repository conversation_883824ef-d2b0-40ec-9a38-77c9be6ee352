import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, Activity, Calendar, DollarSign, 
  TrendingUp, AlertCircle, CheckCircle, Clock,
  UserPlus, MessageSquare, Settings, BarChart3
} from 'lucide-react'
import { useTheme } from '../contexts/ThemeContext'
import { useAuth } from '../contexts/AuthContext'
import { cn } from '../utils/cn'
import { fadeInUp, staggerContainer, staggerItem } from '../utils/animations'

const AdminDashboard = () => {
  const { isDark } = useTheme()
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')

  // Demo data
  const stats = [
    {
      title: 'إجمالي المستخدمين',
      value: '12,847',
      change: '+12%',
      trend: 'up',
      icon: Users,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      title: 'الاستشارات اليوم',
      value: '1,234',
      change: '+8%',
      trend: 'up',
      icon: Activity,
      color: 'from-green-500 to-emerald-500'
    },
    {
      title: 'المواعيد المجدولة',
      value: '856',
      change: '+15%',
      trend: 'up',
      icon: Calendar,
      color: 'from-purple-500 to-pink-500'
    },
    {
      title: 'الإيرادات الشهرية',
      value: '$45,678',
      change: '+23%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-orange-500 to-red-500'
    }
  ]

  const recentActivities = [
    { type: 'user', message: 'مستخدم جديد انضم للمنصة', time: '5 دقائق', status: 'success' },
    { type: 'consultation', message: 'استشارة AI مكتملة', time: '12 دقيقة', status: 'success' },
    { type: 'appointment', message: 'موعد جديد تم حجزه', time: '18 دقيقة', status: 'info' },
    { type: 'payment', message: 'دفعة جديدة تم استلامها', time: '25 دقيقة', status: 'success' },
    { type: 'alert', message: 'تحديث النظام مطلوب', time: '1 ساعة', status: 'warning' }
  ]

  const topDoctors = [
    { name: 'د. أحمد محمد', specialty: 'باطنية', consultations: 156, rating: 4.9 },
    { name: 'د. فاطمة علي', specialty: 'أطفال', consultations: 142, rating: 4.8 },
    { name: 'د. محمد حسن', specialty: 'جراحة', consultations: 128, rating: 4.9 },
    { name: 'د. سارة أحمد', specialty: 'نساء وولادة', consultations: 115, rating: 4.7 }
  ]

  const tabs = [
    { id: 'overview', label: 'نظرة عامة', icon: BarChart3 },
    { id: 'users', label: 'المستخدمين', icon: Users },
    { id: 'consultations', label: 'الاستشارات', icon: MessageSquare },
    { id: 'settings', label: 'الإعدادات', icon: Settings }
  ]

  return (
    <motion.div
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      className="max-w-7xl mx-auto"
    >
      {/* Header */}
      <motion.div variants={staggerItem} className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className={cn(
              "text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",
              isDark && "from-blue-400 to-purple-400"
            )}>
              لوحة التحكم الإدارية
            </h1>
            <p className={cn(
              "text-lg mt-2",
              isDark ? "text-gray-300" : "text-gray-600"
            )}>
              مرحباً {user?.full_name}، إليك نظرة عامة على المنصة
            </p>
          </div>
          
          <motion.div
            whileHover={{ scale: 1.05 }}
            className={cn(
              "px-4 py-2 rounded-lg border",
              isDark 
                ? "bg-green-900/30 text-green-300 border-green-700" 
                : "bg-green-100 text-green-700 border-green-200"
            )}
          >
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
              النظام يعمل بشكل طبيعي
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Demo Credentials Notice */}
      <motion.div 
        variants={staggerItem}
        className={cn(
          "p-4 rounded-lg mb-8 border",
          isDark 
            ? "bg-blue-900/30 text-blue-300 border-blue-700" 
            : "bg-blue-100 text-blue-700 border-blue-200"
        )}
      >
        <div className="flex items-center">
          <AlertCircle className="w-5 h-5 mr-3" />
          <div>
            <p className="font-medium">بيانات تجريبية للإدارة</p>
            <p className="text-sm opacity-80">
              المعلومات المعروضة هنا هي بيانات تجريبية لأغراض العرض التوضيحي
            </p>
          </div>
        </div>
      </motion.div>

      {/* Tabs */}
      <motion.div variants={staggerItem} className="mb-8">
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={cn(
                "flex items-center px-4 py-2 rounded-md text-sm font-medium transition-all",
                activeTab === tab.id
                  ? "bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm"
                  : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
              )}
            >
              <tab.icon className="w-4 h-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </div>
      </motion.div>

      {activeTab === 'overview' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-8"
        >
          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={staggerItem}
                whileHover={{ y: -5, scale: 1.02 }}
                className={cn(
                  "p-6 rounded-2xl border",
                  isDark ? "bg-gray-800/50 border-gray-700" : "bg-white border-gray-200"
                )}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className={cn(
                    "w-12 h-12 rounded-xl flex items-center justify-center",
                    `bg-gradient-to-r ${stat.color}`
                  )}>
                    <stat.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className={cn(
                    "flex items-center text-sm font-medium",
                    stat.trend === 'up' ? "text-green-500" : "text-red-500"
                  )}>
                    <TrendingUp className="w-4 h-4 mr-1" />
                    {stat.change}
                  </div>
                </div>
                <h3 className={cn(
                  "text-2xl font-bold mb-1",
                  isDark ? "text-white" : "text-gray-900"
                )}>
                  {stat.value}
                </h3>
                <p className={cn(
                  "text-sm",
                  isDark ? "text-gray-400" : "text-gray-600"
                )}>
                  {stat.title}
                </p>
              </motion.div>
            ))}
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Recent Activities */}
            <motion.div
              variants={staggerItem}
              className={cn(
                "p-6 rounded-2xl border",
                isDark ? "bg-gray-800/50 border-gray-700" : "bg-white border-gray-200"
              )}
            >
              <h3 className={cn(
                "text-lg font-semibold mb-6",
                isDark ? "text-white" : "text-gray-900"
              )}>
                النشاطات الأخيرة
              </h3>
              <div className="space-y-4">
                {recentActivities.map((activity, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50"
                  >
                    <div className="flex items-center">
                      <div className={cn(
                        "w-2 h-2 rounded-full mr-3",
                        activity.status === 'success' ? "bg-green-500" :
                        activity.status === 'warning' ? "bg-yellow-500" :
                        activity.status === 'info' ? "bg-blue-500" : "bg-gray-500"
                      )} />
                      <span className={cn(
                        "text-sm",
                        isDark ? "text-gray-300" : "text-gray-700"
                      )}>
                        {activity.message}
                      </span>
                    </div>
                    <span className={cn(
                      "text-xs",
                      isDark ? "text-gray-400" : "text-gray-500"
                    )}>
                      {activity.time}
                    </span>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Top Doctors */}
            <motion.div
              variants={staggerItem}
              className={cn(
                "p-6 rounded-2xl border",
                isDark ? "bg-gray-800/50 border-gray-700" : "bg-white border-gray-200"
              )}
            >
              <h3 className={cn(
                "text-lg font-semibold mb-6",
                isDark ? "text-white" : "text-gray-900"
              )}>
                أفضل الأطباء
              </h3>
              <div className="space-y-4">
                {topDoctors.map((doctor, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50"
                  >
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
                        <span className="text-white text-sm font-medium">
                          {doctor.name.charAt(2)}
                        </span>
                      </div>
                      <div>
                        <p className={cn(
                          "font-medium",
                          isDark ? "text-white" : "text-gray-900"
                        )}>
                          {doctor.name}
                        </p>
                        <p className={cn(
                          "text-xs",
                          isDark ? "text-gray-400" : "text-gray-500"
                        )}>
                          {doctor.specialty}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={cn(
                        "text-sm font-medium",
                        isDark ? "text-gray-300" : "text-gray-700"
                      )}>
                        {doctor.consultations} استشارة
                      </p>
                      <p className="text-xs text-yellow-500">
                        ⭐ {doctor.rating}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </motion.div>
      )}

      {activeTab === 'users' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={cn(
            "p-8 rounded-2xl border text-center",
            isDark ? "bg-gray-800/50 border-gray-700" : "bg-white border-gray-200"
          )}
        >
          <Users className={cn(
            "w-16 h-16 mx-auto mb-4",
            isDark ? "text-gray-400" : "text-gray-500"
          )} />
          <h3 className={cn(
            "text-xl font-semibold mb-2",
            isDark ? "text-white" : "text-gray-900"
          )}>
            إدارة المستخدمين
          </h3>
          <p className={cn(
            "text-sm",
            isDark ? "text-gray-400" : "text-gray-600"
          )}>
            هذا القسم قيد التطوير - سيتم إضافة إدارة المستخدمين قريباً
          </p>
        </motion.div>
      )}

      {activeTab === 'consultations' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={cn(
            "p-8 rounded-2xl border text-center",
            isDark ? "bg-gray-800/50 border-gray-700" : "bg-white border-gray-200"
          )}
        >
          <MessageSquare className={cn(
            "w-16 h-16 mx-auto mb-4",
            isDark ? "text-gray-400" : "text-gray-500"
          )} />
          <h3 className={cn(
            "text-xl font-semibold mb-2",
            isDark ? "text-white" : "text-gray-900"
          )}>
            إدارة الاستشارات
          </h3>
          <p className={cn(
            "text-sm",
            isDark ? "text-gray-400" : "text-gray-600"
          )}>
            هذا القسم قيد التطوير - سيتم إضافة إدارة الاستشارات قريباً
          </p>
        </motion.div>
      )}

      {activeTab === 'settings' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={cn(
            "p-8 rounded-2xl border text-center",
            isDark ? "bg-gray-800/50 border-gray-700" : "bg-white border-gray-200"
          )}
        >
          <Settings className={cn(
            "w-16 h-16 mx-auto mb-4",
            isDark ? "text-gray-400" : "text-gray-500"
          )} />
          <h3 className={cn(
            "text-xl font-semibold mb-2",
            isDark ? "text-white" : "text-gray-900"
          )}>
            إعدادات النظام
          </h3>
          <p className={cn(
            "text-sm",
            isDark ? "text-gray-400" : "text-gray-600"
          )}>
            هذا القسم قيد التطوير - سيتم إضافة إعدادات النظام قريباً
          </p>
        </motion.div>
      )}
    </motion.div>
  )
}

export default AdminDashboard
