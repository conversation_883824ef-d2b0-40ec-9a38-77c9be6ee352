"""
<PERSON>ript to create admin user
"""

from sqlalchemy.orm import Session
from models.database import SessionLocal, engine
from models.user import User, UserRole
from services.auth import get_password_hash

def create_admin_user():
    """Create admin user if it doesn't exist"""
    db = SessionLocal()
    
    try:
        # Check if admin user already exists
        admin_user = db.query(User).filter(User.username == "admin").first()
        
        if admin_user:
            print("Admin user already exists!")
            return
        
        # Create admin user
        hashed_password = get_password_hash("admin123")
        admin_user = User(
            email="<EMAIL>",
            username="admin",
            full_name="System Administrator",
            hashed_password=hashed_password,
            role=UserRole.ADMIN,
            is_active=True,
            is_verified=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print("Admin user created successfully!")
        print(f"Username: admin")
        print(f"Password: admin123")
        print(f"Email: <EMAIL>")
        
    except Exception as e:
        print(f"Error creating admin user: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()
