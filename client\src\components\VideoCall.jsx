import React, { useEffect, useRef, useState } from 'react'
import DailyIframe from '@daily-co/daily-js'

const VideoCall = ({ roomUrl, onLeave }) => {
  const callFrameRef = useRef(null)
  const [callFrame, setCallFrame] = useState(null)
  const [participants, setParticipants] = useState({})
  const [isJoined, setIsJoined] = useState(false)

  useEffect(() => {
    if (!roomUrl) return

    // Create Daily call frame
    const frame = DailyIframe.createFrame(callFrameRef.current, {
      iframeStyle: {
        width: '100%',
        height: '100%',
        border: 'none',
        borderRadius: '8px'
      },
      showLeaveButton: true,
      showFullscreenButton: true,
    })

    setCallFrame(frame)

    // Event listeners
    frame.on('joined-meeting', () => {
      setIsJoined(true)
    })

    frame.on('left-meeting', () => {
      setIsJoined(false)
      if (onLeave) onLeave()
    })

    frame.on('participant-joined', (event) => {
      setParticipants(prev => ({
        ...prev,
        [event.participant.session_id]: event.participant
      }))
    })

    frame.on('participant-left', (event) => {
      setParticipants(prev => {
        const updated = { ...prev }
        delete updated[event.participant.session_id]
        return updated
      })
    })

    // Join the room
    frame.join({ url: roomUrl })

    // Cleanup
    return () => {
      if (frame) {
        frame.destroy()
      }
    }
  }, [roomUrl, onLeave])

  const leaveCall = () => {
    if (callFrame) {
      callFrame.leave()
    }
  }

  return (
    <div className="w-full h-full min-h-[500px] bg-gray-900 rounded-lg overflow-hidden">
      <div ref={callFrameRef} className="w-full h-full" />
      
      {/* Call Controls */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-4">
        <button
          onClick={leaveCall}
          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
        >
          Leave Call
        </button>
      </div>
    </div>
  )
}

export default VideoCall
