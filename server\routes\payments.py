"""
Payment and subscription routes
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import stripe
import os

from models.database import get_db
from models.user import User, SubscriptionPlan
from services.auth import get_current_active_user

# Configure Stripe
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

router = APIRouter()

from pydantic import BaseModel

class SubscriptionRequest(BaseModel):
    plan: SubscriptionPlan

class PaymentIntentResponse(BaseModel):
    client_secret: str
    subscription_plan: SubscriptionPlan

@router.post("/create-payment-intent", response_model=PaymentIntentResponse)
async def create_payment_intent(
    subscription_request: SubscriptionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a payment intent for subscription"""
    try:
        # Define pricing (in cents)
        pricing = {
            SubscriptionPlan.BASIC: 999,  # $9.99
            SubscriptionPlan.PREMIUM: 1999  # $19.99
        }
        
        if subscription_request.plan not in pricing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid subscription plan"
            )
        
        # Create or retrieve Stripe customer
        if not current_user.stripe_customer_id:
            customer = stripe.Customer.create(
                email=current_user.email,
                name=current_user.full_name
            )
            current_user.stripe_customer_id = customer.id
            db.commit()
        
        # Create payment intent
        intent = stripe.PaymentIntent.create(
            amount=pricing[subscription_request.plan],
            currency='usd',
            customer=current_user.stripe_customer_id,
            metadata={
                'user_id': current_user.id,
                'subscription_plan': subscription_request.plan.value
            }
        )
        
        return PaymentIntentResponse(
            client_secret=intent.client_secret,
            subscription_plan=subscription_request.plan
        )
        
    except stripe.error.StripeError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Stripe error: {str(e)}"
        )

@router.post("/webhook")
async def stripe_webhook(request: dict, db: Session = Depends(get_db)):
    """Handle Stripe webhooks"""
    # This is a simplified webhook handler
    # In production, you should verify the webhook signature
    
    if request.get('type') == 'payment_intent.succeeded':
        payment_intent = request['data']['object']
        user_id = payment_intent['metadata'].get('user_id')
        subscription_plan = payment_intent['metadata'].get('subscription_plan')
        
        if user_id and subscription_plan:
            user = db.query(User).filter(User.id == int(user_id)).first()
            if user:
                user.subscription_plan = SubscriptionPlan(subscription_plan)
                db.commit()
    
    return {"status": "success"}

@router.get("/subscription")
async def get_subscription(current_user: User = Depends(get_current_active_user)):
    """Get current user's subscription information"""
    return {
        "subscription_plan": current_user.subscription_plan,
        "stripe_customer_id": current_user.stripe_customer_id
    }
