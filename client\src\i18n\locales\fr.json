{"common": {"loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "cancel": "Annuler", "save": "Enregistrer", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmer", "back": "Retour", "next": "Suivant", "previous": "Précédent", "close": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON>", "refresh": "Actualiser"}, "nav": {"home": "Accueil", "dashboard": "Tableau de bord", "aiConsultation": "Consultation IA", "appointments": "<PERSON><PERSON><PERSON>vous", "profile": "Profil", "features": "Fonctionnalités", "about": "À propos", "demo": "Essayer la démo", "login": "Connexion", "register": "S'inscrire", "logout": "Déconnexion", "getStarted": "Commencer", "admin": "<PERSON><PERSON><PERSON>min"}, "home": {"welcome": "Bienvenue dans l'avenir des soins de santé", "title": "Bienvenue sur", "subtitle": "Plateforme de santé alimentée par l'IA connectant les patients aux spécialistes grâce à l'intelligence artificielle et aux consultations vidéo", "startFree": "Commencer gratuitement", "tryApp": "Essayer l'app", "goToDashboard": "<PERSON>er au tableau de bord", "whyChoose": "Pourquoi choisir", "discoverFuture": "Découvrez l'avenir des soins de santé avec notre plateforme complète et avancée", "readyToTransform": "Prêt à transformer votre expérience de santé ?", "joinThousands": "Rejoignez des milliers de patients et de professionnels de santé sur Si7ati", "startJourney": "Commencez votre parcours aujourd'hui", "demoCredentials": "Identifiants de démonstration pour les tests", "regularUser": "Utilisateur régulier :", "adminUser": "Administrateur système :", "username": "Nom d'utilisateur :", "password": "Mot de passe :"}, "features": {"title": "Fonctionnalités de Si7ati", "subtitle": "Découvrez comment Si7ati redéfinit les soins de santé numériques grâce à une technologie avancée et des services médicaux complets", "aiConsultation": {"title": "IA Avancée", "description": "Système d'IA avancé pour l'analyse des symptômes et un diagnostic préliminaire précis", "features": ["<PERSON><PERSON><PERSON> des symptômes", "Diagnostic intelligent", "Conseils médicaux", "Apprentissage continu"]}, "videoConsultations": {"title": "Consultations vidéo", "description": "Connectez-vous avec des médecins spécialistes via des appels vidéo sécurisés et de haute qualité", "features": ["Appels HD", "Réservation instantanée", "Médecins certifiés", "Do<PERSON><PERSON> m<PERSON>"]}, "security": {"title": "Sécurité et confidentialité", "description": "Protection avancée de vos données médicales avec chiffrement complet et conformité internationale", "features": ["Chiffrement complet", "Conforme HIPAA", "Confidentialité totale", "<PERSON><PERSON><PERSON><PERSON>"]}, "available247": {"title": "Disponible 24h/24 7j/7", "description": "Service médical disponible 24h/24 7j/7 pour vous assurer d'obtenir de l'aide quand vous en avez besoin", "features": ["Service continu", "Réponse rapide", "Urgence", "Support instantané"]}, "medicalTeam": {"title": "Équipe médicale spécialisée", "description": "Réseau de médecins certifiés et de spécialistes dans divers domaines médicaux", "features": ["Médecins certifiés", "Spécialités diverses", "Haute expérience", "Avis des patients"]}, "easyToUse": {"title": "Facile à utiliser", "description": "Interface simple et facile à utiliser adaptée à tous les âges et niveaux techniques", "features": ["Interface intuitive", "Design réactif", "Support multilingue", "Instructions claires"]}, "stats": {"activeUsers": "Utilisateurs actifs", "certifiedDoctors": "Médecins certifiés", "uptime": "Temps de fonctionnement", "userRating": "Note des utilisateurs"}, "advancedTechnology": "Technologie avancée", "technologySubtitle": "Nous utilisons les dernières technologies pour garantir la meilleure expérience médicale numérique", "artificialIntelligence": "Intelligence artificielle", "aiDescription": "Algorithmes avancés pour le diagnostic", "advancedSecurity": "Sécurité avancée", "securityDescription": "Chiffrement de niveau militaire", "fastPerformance": "Performance rapide", "performanceDescription": "Réponse instantanée et haute fiabilité", "readyForFuture": "<PERSON><PERSON><PERSON><PERSON> à découvrir l'avenir ?", "joinUsers": "Rejoignez des milliers d'utilisateurs qui font confiance à Si7ati pour leurs soins de santé"}, "about": {"title": "À propos de nous", "subtitle": "Si7ati est une plateforme leader dans les soins de santé numériques, visant à rendre les services médicaux plus accessibles à tous grâce à une technologie avancée et à l'intelligence artificielle", "globalService": "Service mondial - Soins locaux", "ourVision": "Notre vision", "visionText": "Être la plateforme leader mondiale dans les soins de santé numériques, où chacun peut accéder facilement et en toute sécurité à des soins médicaux de haute qualité.", "ourMission": "Notre mission", "missionText": "Permettre aux individus de gérer efficacement leur santé en fournissant des outils intelligents et des services médicaux avancés qui combinent l'expertise humaine et la technologie innovante.", "coreValues": "Nos valeurs fondamentales", "valuesSubtitle": "Les principes qui guident notre travail et façonnent notre identité", "careFirst": "Les soins d'abord", "careDescription": "Nous plaçons toujours la santé et le bien-être des patients en premier", "trustSecurity": "Confiance et sécurité", "trustDescription": "Protection de vos données médicales avec les plus hauts standards de sécurité", "continuousInnovation": "Innovation continue", "innovationDescription": "Nous développons la technologie pour améliorer les services médicaux", "collaboration": "Collaboration", "collaborationDescription": "Nous travaillons avec les meilleurs médecins et spécialistes", "ourTeam": "Notre équipe", "teamSubtitle": "Experts spécialisés travaillant pour réaliser notre vision", "chiefMedicalOfficer": "Directeur mé<PERSON>al", "chiefTechnologyOfficer": "Directeur de la technologie", "productManager": "Chef de produit", "testimonials": "Ce que disent nos clients", "testimonialsSubtitle": "Expériences réelles des utilisateurs de Si7ati", "joinDevelopment": "Re<PERSON><PERSON><PERSON> le parcours de développement", "bePartOfFuture": "Faites partie de l'avenir des soins de santé numériques", "startWithUs": "Commencez avec nous aujourd'hui"}, "demo": {"title": "Démo IA Si7ati", "subtitle": "Essayez l'assistant intelligent de Si7ati gratuitement", "freeTrialNoRegistration": "Essai gratuit - Aucune inscription requise", "welcomeMessage": "Bonjour ! Je suis l'assistant intelligent de Si7ati. Je peux vous aider à analyser les symptômes et fournir des conseils médicaux initiaux. Comment puis-je vous aider aujourd'hui ?", "typeSymptoms": "Tapez vos symptômes ici...", "commonSymptoms": "<PERSON>ympt<PERSON>s courants", "importantNote": "Note importante", "demoLimitation": "Ceci est une version de démonstration simplifiée. Pour un diagnostic précis et avancé, veuillez vous inscrire sur Si7ati.", "readyToStart": "Prêt à commencer ?", "getAdvanced": "Obtenez un diagnostic avancé et des services médicaux complets", "createFreeAccount": "<PERSON><PERSON><PERSON> un compte gratuit", "connectedNow": "Connecté maintenant", "send": "Envoyer"}, "auth": {"signInToAccount": "Connectez-vous à votre compte", "createAccount": "<PERSON><PERSON><PERSON> un compte", "orCreateNew": "Ou créez un nouveau compte", "orSignInExisting": "Ou connectez-vous à votre compte existant", "fullName": "Nom complet", "email": "Adresse e-mail", "username": "Nom d'utilisateur", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "accountType": "Type de compte", "patient": "Patient", "doctor": "Médecin", "phoneNumber": "Numéro de téléphone", "gender": "Genre", "selectGender": "Sélectionner le genre", "male": "<PERSON><PERSON>", "female": "<PERSON>mme", "other": "<PERSON><PERSON>", "signIn": "Se connecter", "enterFullName": "Entrez votre nom complet", "enterEmail": "Entrez votre e-mail", "chooseUsername": "Choisissez un nom d'utilisateur", "enterPassword": "Entrez votre mot de passe", "confirmYourPassword": "Confirmez votre mot de passe", "demoCredentials": "Identifiants de démonstration :", "loginSuccessful": "Connexion réussie !", "registrationSuccessful": "Inscription réussie ! Veuillez vous connecter.", "loginFailed": "Échec de la connexion", "registrationFailed": "Échec de l'inscription", "passwordsDontMatch": "Les mots de passe ne correspondent pas"}, "dashboard": {"welcomeBack": "Bon retour", "overviewToday": "Voici ce que vous pouvez faire avec Si7ati aujourd'hui", "aiConsultation": "Consultation IA", "aiDescription": "Obtenez des conseils médicaux instantanés basés sur vos symptômes", "bookAppointment": "<PERSON><PERSON><PERSON> rendez-vous", "appointmentDescription": "Planifiez une consultation vidéo avec un médecin", "viewProfile": "Voir le profil", "profileDescription": "Mettez à jour vos informations personnelles et médicales", "aiConsultations": "Consultations IA", "appointments": "<PERSON><PERSON><PERSON>vous", "upcoming": "À venir", "records": "Dossiers", "recentActivity": "Activité récente", "noRecentActivity": "Aucune activité récente", "startByGetting": "Commencez par obtenir une consultation IA ou prendre un rendez-vous"}, "admin": {"title": "Tableau de bord administrateur", "welcomeAdmin": "Bienvenue {{name}}, voici un aperçu de la plateforme", "systemRunning": "Système fonctionnant normalement", "demoDataNotice": "Données de démonstration pour l'administration", "demoDataDescription": "Les informations affichées ici sont des données de démonstration à des fins de présentation", "overview": "<PERSON><PERSON><PERSON><PERSON>", "users": "Utilisateurs", "consultations": "Consultations", "settings": "Paramètres", "totalUsers": "Total des utilisateurs", "consultationsToday": "Consultations aujourd'hui", "scheduledAppointments": "<PERSON><PERSON><PERSON>vous programmés", "monthlyRevenue": "<PERSON><PERSON><PERSON> mensuels", "recentActivities": "Activités récentes", "topDoctors": "Meilleurs médecins", "userManagement": "Gestion des utilisateurs", "userManagementDescription": "Cette section est en cours de développement - la gestion des utilisateurs sera ajoutée bientôt", "consultationManagement": "Gestion des consultations", "consultationManagementDescription": "Cette section est en cours de développement - la gestion des consultations sera ajoutée bientôt", "systemSettings": "Paramètres système", "systemSettingsDescription": "Cette section est en cours de développement - les paramètres système seront ajoutés bientôt"}, "consultation": {"title": "Consultation médicale IA", "subtitle": "Obtenez des conseils médicaux instantanés basés sur vos symptômes. Ceci ne remplace pas les soins médicaux professionnels.", "tellUsSymptoms": "Parlez-nous de vos symptômes", "age": "Âge", "enterAge": "Entrez votre âge", "gender": "Genre", "symptoms": "<PERSON><PERSON>pt<PERSON><PERSON>", "describeSymptoms": "Décrivez vos symptômes séparés par des virgules (ex: mal de tête, nausée, fièvre)", "separateSymptoms": "<PERSON><PERSON><PERSON><PERSON> plusieurs symptômes par des virgules", "getConsultation": "Obtenir une consultation IA", "analyzing": "Analyse en cours...", "consultationResults": "Résultats de la consultation", "urgencyLevel": "Niveau d'urgence", "medicalAdvice": "Conseils médicaux", "suggestedAction": "Action suggérée", "confidenceScore": "Score de confiance", "newConsultation": "Nouvelle consultation", "bookAppointment": "<PERSON><PERSON><PERSON> rendez-vous", "noConsultationYet": "Aucune consultation encore", "fillFormForConsultation": "Remplissez le formulaire pour obtenir votre consultation médicale alimentée par l'IA", "importantDisclaimer": "Avertissement important", "disclaimerText": "Cette consultation IA est à des fins d'information uniquement et ne doit pas remplacer les conseils médicaux professionnels. Consultez toujours un professionnel de santé qualifié pour les symptômes graves.", "high": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "low": "Faible"}, "appointments": {"title": "<PERSON><PERSON><PERSON>vous", "subtitle": "<PERSON><PERSON>rez vos rendez-vous et planifiez de nouvelles consultations", "comingSoon": "<PERSON><PERSON><PERSON>vous bi<PERSON><PERSON><PERSON> disponibles", "workingOnSystem": "Nous travaillons sur le système de réservation de rendez-vous. Pour l'instant, vous pouvez utiliser notre fonction de consultation IA.", "tryAiConsultation": "Essayer la consultation IA"}, "profile": {"title": "Profil", "subtitle": "<PERSON><PERSON><PERSON> vos informations personnelles et paramètres de compte", "personalInformation": "Informations personnelles", "accountInformation": "Informations du compte", "fullName": "Nom complet", "email": "E-mail", "username": "Nom d'utilisateur", "phoneNumber": "Numéro de téléphone", "gender": "Genre", "accountType": "Type de compte", "subscriptionPlan": "Plan d'abonnement", "accountStatus": "Statut du compte", "emailVerified": "E-mail vérifié", "active": "Actif", "inactive": "Inactif", "verified": "Vérifié", "pending": "En attente", "notProvided": "Non fourni", "free": "<PERSON><PERSON><PERSON>", "editProfile": "Modifier le profil"}, "subscription": {"title": "Choisissez votre plan", "subtitle": "Sélectionnez le plan parfait pour vos besoins de santé. Met<PERSON>z à niveau ou rétrogradez à tout moment.", "currentPlan": "Plan actuel", "upgradeTo": "Passer à {{plan}}", "free": "<PERSON><PERSON><PERSON>", "basic": "Basique", "premium": "Premium", "month": "mois", "forever": "pour toujours", "freeFeatures": ["Consultations IA de base", "<PERSON><PERSON><PERSON>vous limités", "Suivi de santé de base", "Support par e-mail"], "basicFeatures": ["Consultations IA illimitées", "<PERSON><PERSON><PERSON>vous vidéo", "<PERSON><PERSON><PERSON> <PERSON> sant<PERSON> a<PERSON>", "Support prioritaire", "Stockage des dossiers médicaux"], "premiumFeatures": ["Tout dans Basique", "Consultations de spécialistes", "Compte familial (jusqu'à 4 membres)", "Support d'urgence 24h/24 7j/7", "Gestion des prescriptions", "Analyses et insights santé"], "billingInformation": "Informations de facturation", "currentSubscription": "Abonnement actuel", "currentlyOn": "Vous êtes actuellement sur le plan {{plan}}.", "paymentMethod": "Méthode de paiement", "noPaymentMethod": "Aucune méthode de paiement enregistrée. Ajoutez une méthode de paiement pour mettre à niveau votre plan.", "nextBillingDate": "Prochaine date de facturation", "noBillingRequired": "Aucune facturation requise", "notAvailableDemo": "Non disponible en démo", "billingHistory": "Historique de facturation", "noBillingHistory": "Aucun historique de facturation disponible.", "faq": "Questions fréquemment posées", "canChangePlan": "Puis-je changer mon plan à tout moment ?", "canChangePlanAnswer": "<PERSON><PERSON>, vous pouvez mettre à niveau ou rétrograder votre plan à tout moment. Les changements seront reflétés dans votre prochain cycle de facturation.", "isThereFreeTrial": "Y a-t-il un essai gratuit ?", "freeTrialAnswer": "Tous les nouveaux utilisateurs commencent avec notre plan Gratuit. Vous pouvez passer aux plans payants quand vous avez besoin de fonctionnalités supplémentaires.", "paymentMethods": "Quelles méthodes de paiement acceptez-vous ?", "paymentMethodsAnswer": "Nous acceptons toutes les principales cartes de crédit, cartes de débit et portefeuilles numériques via notre processeur de paiement sécurisé.", "canCancelAnytime": "Puis-je annuler à tout moment ?", "cancelAnytimeAnswer": "<PERSON><PERSON>, vous pouvez annuler votre abonnement à tout moment. Vous continuerez à avoir accès jusqu'à la fin de votre période de facturation."}}