"""
Appointment management routes
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime

from models.database import get_db
from models.user import User, UserRole
from models.appointment import Appointment, AppointmentStatus
from services.auth import get_current_active_user

router = APIRouter()

# Placeholder schemas (to be expanded)
from pydantic import BaseModel

class AppointmentCreate(BaseModel):
    doctor_id: int
    title: str
    description: str = None
    scheduled_at: datetime
    duration_minutes: int = 30

class AppointmentResponse(BaseModel):
    id: int
    patient_id: int
    doctor_id: int
    title: str
    description: str = None
    status: AppointmentStatus
    scheduled_at: datetime
    duration_minutes: int
    created_at: datetime
    
    class Config:
        from_attributes = True

@router.post("/", response_model=AppointmentResponse)
async def create_appointment(
    appointment: AppointmentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new appointment"""
    # Verify doctor exists
    doctor = db.query(User).filter(
        User.id == appointment.doctor_id,
        User.role == UserRole.DOCTOR,
        User.is_active == True
    ).first()
    
    if not doctor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Doctor not found"
        )
    
    # Create appointment
    db_appointment = Appointment(
        patient_id=current_user.id,
        doctor_id=appointment.doctor_id,
        title=appointment.title,
        description=appointment.description,
        scheduled_at=appointment.scheduled_at,
        duration_minutes=appointment.duration_minutes
    )
    
    db.add(db_appointment)
    db.commit()
    db.refresh(db_appointment)
    
    return db_appointment

@router.get("/", response_model=List[AppointmentResponse])
async def get_appointments(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get user's appointments"""
    if current_user.role == UserRole.PATIENT:
        appointments = db.query(Appointment)\
            .filter(Appointment.patient_id == current_user.id)\
            .order_by(Appointment.scheduled_at.desc())\
            .all()
    elif current_user.role == UserRole.DOCTOR:
        appointments = db.query(Appointment)\
            .filter(Appointment.doctor_id == current_user.id)\
            .order_by(Appointment.scheduled_at.desc())\
            .all()
    else:  # Admin
        appointments = db.query(Appointment)\
            .order_by(Appointment.scheduled_at.desc())\
            .all()
    
    return appointments

@router.get("/{appointment_id}", response_model=AppointmentResponse)
async def get_appointment(
    appointment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get appointment by ID"""
    appointment = db.query(Appointment).filter(Appointment.id == appointment_id).first()
    
    if not appointment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Appointment not found"
        )
    
    # Check permissions
    if (current_user.role == UserRole.PATIENT and appointment.patient_id != current_user.id) or \
       (current_user.role == UserRole.DOCTOR and appointment.doctor_id != current_user.id):
        if current_user.role != UserRole.ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
    
    return appointment
