import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import {
  Stethoscope, Bot, Video, Shield, Clock, Users,
  Heart, Star, ArrowRight, Play, CheckCircle, Sparkles
} from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { useTheme } from '../contexts/ThemeContext'
import { useLanguage } from '../contexts/LanguageContext'
import { cn } from '../utils/cn'
import { fadeInUp, staggerContainer, staggerItem, hoverLift } from '../utils/animations'

const Home = () => {
  const { isAuthenticated } = useAuth()
  const { isDark } = useTheme()
  const { isRTL } = useLanguage()
  const { t } = useTranslation()

  return (
    <div className={cn(
      "min-h-screen transition-colors duration-300",
      isDark
        ? "bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900"
        : "bg-gradient-to-br from-blue-50 via-white to-purple-50"
    )}>
      {/* Hero Section */}
      <motion.div
        variants={staggerContainer}
        initial="initial"
        animate="animate"
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16"
      >
        <motion.div variants={staggerItem} className="text-center relative">
          {/* Floating Elements */}
          <motion.div
            animate={{
              y: [0, -20, 0],
              rotate: [0, 5, 0]
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-10 left-10 w-20 h-20 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 blur-xl"
          />
          <motion.div
            animate={{
              y: [0, 20, 0],
              rotate: [0, -5, 0]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute top-20 right-20 w-32 h-32 bg-gradient-to-r from-pink-400 to-red-400 rounded-full opacity-20 blur-xl"
          />

          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring" }}
            className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 border border-blue-200 dark:border-blue-700 mb-8"
          >
            <Sparkles className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
            <span className={cn(
              "text-sm font-medium",
              isDark ? "text-blue-300" : "text-blue-700"
            )}>
              {t('home.welcome')}
            </span>
          </motion.div>

          <motion.h1
            variants={fadeInUp}
            className={cn(
              "text-5xl md:text-7xl font-bold mb-6 leading-tight",
              "bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent",
              isDark && "from-blue-400 via-purple-400 to-pink-400"
            )}
          >
            {t('home.title')}{' '}
            <motion.span
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"]
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                ease: "linear"
              }}
              className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent bg-[length:200%_auto]"
            >
              Si7ati
            </motion.span>
          </motion.h1>

          <motion.p
            variants={fadeInUp}
            className={cn(
              "text-xl md:text-2xl mb-8 max-w-4xl mx-auto leading-relaxed",
              isDark ? "text-gray-300" : "text-gray-600"
            )}
          >
            {t('home.subtitle')}
          </motion.p>

          {!isAuthenticated ? (
            <motion.div
              variants={fadeInUp}
              className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  to="/register"
                  className="group relative bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl inline-flex items-center"
                >
                  {t('home.startFree')}
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-xl"
                    initial={{ x: '-100%' }}
                    whileHover={{ x: '100%' }}
                    transition={{ duration: 0.6 }}
                  />
                </Link>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  to="/demo"
                  className={cn(
                    "group border-2 px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 inline-flex items-center",
                    isDark
                      ? "border-gray-600 text-gray-300 hover:border-gray-500 hover:bg-gray-800"
                      : "border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50"
                  )}
                >
                  <Play className="w-5 h-5 mr-2" />
                  {t('home.tryApp')}
                </Link>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  to="/login"
                  className={cn(
                    "px-6 py-4 rounded-xl text-lg font-medium transition-all duration-300",
                    isDark
                      ? "text-gray-300 hover:text-white hover:bg-gray-800"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  )}
                >
                  {t('nav.login')}
                </Link>
              </motion.div>
            </motion.div>
          ) : (
            <motion.div variants={fadeInUp}>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  to="/dashboard"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl inline-flex items-center"
                >
                  {t('home.goToDashboard')}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </motion.div>
            </motion.div>
          )}

          {/* Demo Credentials */}
          <motion.div
            variants={fadeInUp}
            className={cn(
              "mt-12 p-6 rounded-2xl border backdrop-blur-sm",
              isDark
                ? "bg-gray-800/30 border-gray-700"
                : "bg-white/30 border-gray-200"
            )}
          >
            <h3 className={cn(
              "text-lg font-semibold mb-4 text-center",
              isDark ? "text-white" : "text-gray-900"
            )}>
              {t('home.demoCredentials')}
            </h3>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div className={cn(
                "p-4 rounded-lg",
                isDark ? "bg-gray-700/50" : "bg-gray-100/50"
              )}>
                <p className={cn(
                  "font-medium mb-2",
                  isDark ? "text-blue-400" : "text-blue-600"
                )}>
                  {t('home.regularUser')}
                </p>
                <p className={cn(isDark ? "text-gray-300" : "text-gray-700")}>
                  {t('home.username')} <span className="font-mono">testuser</span>
                </p>
                <p className={cn(isDark ? "text-gray-300" : "text-gray-700")}>
                  {t('home.password')} <span className="font-mono">testpassword123</span>
                </p>
              </div>
              <div className={cn(
                "p-4 rounded-lg",
                isDark ? "bg-gray-700/50" : "bg-gray-100/50"
              )}>
                <p className={cn(
                  "font-medium mb-2",
                  isDark ? "text-purple-400" : "text-purple-600"
                )}>
                  {t('home.adminUser')}
                </p>
                <p className={cn(isDark ? "text-gray-300" : "text-gray-700")}>
                  {t('home.username')} <span className="font-mono">admin</span>
                </p>
                <p className={cn(isDark ? "text-gray-300" : "text-gray-700")}>
                  {t('home.password')} <span className="font-mono">admin123</span>
                </p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Features Section */}
      <motion.div
        variants={staggerContainer}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20"
      >
        <motion.div variants={staggerItem} className="text-center mb-16">
          <motion.h2
            className={cn(
              "text-4xl md:text-5xl font-bold mb-6",
              isDark ? "text-white" : "text-gray-900"
            )}
          >
            {t('home.whyChoose')}{' '}
            <span className={cn(
              "bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",
              isDark && "from-blue-400 to-purple-400"
            )}>
              Si7ati
            </span>
            ؟
          </motion.h2>
          <motion.p
            className={cn(
              "text-xl max-w-3xl mx-auto",
              isDark ? "text-gray-300" : "text-gray-600"
            )}
          >
            {t('home.discoverFuture')}
          </motion.p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8">
          {[
            {
              icon: Bot,
              titleKey: 'features.aiConsultation.title',
              descriptionKey: 'features.aiConsultation.description',
              color: 'from-blue-500 to-cyan-500'
            },
            {
              icon: Video,
              titleKey: 'features.videoConsultations.title',
              descriptionKey: 'features.videoConsultations.description',
              color: 'from-green-500 to-emerald-500'
            },
            {
              icon: Shield,
              titleKey: 'features.security.title',
              descriptionKey: 'features.security.description',
              color: 'from-purple-500 to-pink-500'
            }
          ].map((feature, index) => (
            <motion.div
              key={index}
              variants={staggerItem}
              whileHover={{ y: -10, scale: 1.02 }}
              className={cn(
                "p-8 rounded-3xl border backdrop-blur-sm transition-all duration-300 group",
                isDark
                  ? "bg-gray-800/30 border-gray-700 hover:border-gray-600"
                  : "bg-white/30 border-gray-200 hover:border-gray-300"
              )}
            >
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                whileInView={{ scale: 1, rotate: 0 }}
                transition={{ delay: index * 0.1, type: "spring" }}
                viewport={{ once: true }}
                className={cn(
                  "w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 relative overflow-hidden",
                  `bg-gradient-to-r ${feature.color}`
                )}
              >
                <feature.icon className="w-8 h-8 text-white relative z-10" />
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"
                  animate={{ x: [-100, 100] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                />
              </motion.div>

              <h3 className={cn(
                "text-xl font-bold mb-2 text-center",
                isDark ? "text-white" : "text-gray-900"
              )}>
                {t(feature.titleKey)}
              </h3>

              <p className={cn(
                "text-center leading-relaxed",
                isDark ? "text-gray-300" : "text-gray-600"
              )}>
                {t(feature.descriptionKey)}
              </p>

              <motion.div
                className={cn(
                  "mt-6 h-1 rounded-full mx-auto",
                  `bg-gradient-to-r ${feature.color}`
                )}
                initial={{ width: 0 }}
                whileInView={{ width: "60%" }}
                transition={{ delay: index * 0.1 + 0.5, duration: 0.8 }}
                viewport={{ once: true }}
              />
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* CTA Section */}
      <motion.div
        variants={staggerContainer}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        className="relative overflow-hidden"
      >
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600"
          animate={{
            backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{ backgroundSize: "200% 200%" }}
        />

        <div className="relative max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 py-20">
          <motion.div variants={staggerItem}>
            <Star className="w-16 h-16 text-yellow-400 mx-auto mb-6" />
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              {t('home.readyToTransform')}
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              {t('home.joinThousands')}
            </p>

            {!isAuthenticated && (
              <motion.div
                variants={fadeInUp}
                className="flex flex-col sm:flex-row gap-4 justify-center"
              >
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Link
                    to="/register"
                    className="bg-white hover:bg-gray-100 text-blue-600 px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl inline-flex items-center"
                  >
                    {t('home.startJourney')}
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Link>
                </motion.div>

                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Link
                    to="/demo"
                    className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 inline-flex items-center"
                  >
                    <Play className="w-5 h-5 mr-2" />
                    {t('home.tryApp')}
                  </Link>
                </motion.div>
              </motion.div>
            )}
          </motion.div>
        </div>

        {/* Floating Elements */}
        <motion.div
          animate={{
            y: [0, -20, 0],
            rotate: [0, 10, 0]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"
        />
        <motion.div
          animate={{
            y: [0, 20, 0],
            rotate: [0, -10, 0]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute bottom-10 right-10 w-32 h-32 bg-white/10 rounded-full blur-xl"
        />
      </motion.div>
    </div>
  )
}

export default Home
