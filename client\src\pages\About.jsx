import React from 'react'
import { motion } from 'framer-motion'
import { 
  Heart, Target, Users, Globe, Award, 
  Lightbulb, Shield, Zap, Star, Quote 
} from 'lucide-react'
import { useTheme } from '../contexts/ThemeContext'
import { cn } from '../utils/cn'
import { fadeInUp, staggerContainer, staggerItem, hoverLift } from '../utils/animations'

const About = () => {
  const { isDark } = useTheme()

  const values = [
    {
      icon: Heart,
      title: 'الرعاية أولاً',
      description: 'نضع صحة المرضى ورفاهيتهم في المقدمة دائماً',
      color: 'from-red-500 to-pink-500'
    },
    {
      icon: Shield,
      title: 'الأمان والثقة',
      description: 'حماية بياناتك الطبية بأعلى معايير الأمان',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Lightbulb,
      title: 'الابتكار المستمر',
      description: 'نطور التكنولوجيا لتحسين الخدمات الطبية',
      color: 'from-yellow-500 to-orange-500'
    },
    {
      icon: Users,
      title: 'التعاون',
      description: 'نعمل مع أفضل الأطباء والمختصين',
      color: 'from-green-500 to-emerald-500'
    }
  ]

  const team = [
    {
      name: 'د. أحمد محمد',
      role: 'المدير الطبي',
      roleEn: 'Chief Medical Officer',
      image: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=400&fit=crop&crop=face',
      bio: 'خبرة 15 عاماً في الطب الباطني والتكنولوجيا الطبية'
    },
    {
      name: 'سارة أحمد',
      role: 'مديرة التكنولوجيا',
      roleEn: 'Chief Technology Officer',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
      bio: 'متخصصة في الذكاء الاصطناعي والتطبيقات الطبية'
    },
    {
      name: 'محمد علي',
      role: 'مدير المنتج',
      roleEn: 'Product Manager',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
      bio: 'خبير في تطوير المنتجات الرقمية والتجربة المستخدم'
    }
  ]

  const testimonials = [
    {
      name: 'فاطمة أحمد',
      role: 'مريضة',
      content: 'Si7ati غيرت طريقة تعاملي مع صحتي. الخدمة سريعة ودقيقة والأطباء محترفون جداً.',
      rating: 5
    },
    {
      name: 'د. خالد محمود',
      role: 'طبيب أطفال',
      content: 'منصة ممتازة تساعدني في الوصول لمرضى أكثر وتقديم استشارات عالية الجودة.',
      rating: 5
    },
    {
      name: 'عمر حسن',
      role: 'مستخدم',
      content: 'التطبيق سهل الاستخدام والذكاء الاصطناعي يقدم نصائح مفيدة جداً.',
      rating: 5
    }
  ]

  return (
    <motion.div
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      className="max-w-7xl mx-auto"
    >
      {/* Hero Section */}
      <motion.div variants={staggerItem} className="text-center mb-20">
        <motion.h1 
          className={cn(
            "text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent",
            isDark && "from-blue-400 via-purple-400 to-pink-400"
          )}
        >
          من نحن
        </motion.h1>
        <motion.p 
          className={cn(
            "text-xl max-w-4xl mx-auto leading-relaxed mb-8",
            isDark ? "text-gray-300" : "text-gray-600"
          )}
        >
          Si7ati هي منصة رائدة في مجال الرعاية الصحية الرقمية، تهدف إلى جعل الخدمات الطبية أكثر سهولة ووصولاً للجميع من خلال التكنولوجيا المتقدمة والذكاء الاصطناعي
        </motion.p>
        
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.5, type: "spring" }}
          className={cn(
            "inline-flex items-center px-6 py-3 rounded-full text-sm font-medium",
            isDark 
              ? "bg-blue-900/30 text-blue-300 border border-blue-700" 
              : "bg-blue-100 text-blue-700 border border-blue-200"
          )}
        >
          <Globe className="w-4 h-4 mr-2" />
          خدمة عالمية - رعاية محلية
        </motion.div>
      </motion.div>

      {/* Mission & Vision */}
      <motion.div variants={staggerItem} className="grid md:grid-cols-2 gap-12 mb-20">
        <motion.div
          whileHover={{ scale: 1.02 }}
          className={cn(
            "p-8 rounded-3xl",
            isDark 
              ? "bg-gradient-to-br from-blue-900/50 to-purple-900/50 border border-blue-700" 
              : "bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200"
          )}
        >
          <Target className={cn(
            "w-12 h-12 mb-6",
            isDark ? "text-blue-400" : "text-blue-600"
          )} />
          <h2 className={cn(
            "text-2xl font-bold mb-4",
            isDark ? "text-white" : "text-gray-900"
          )}>
            رؤيتنا
          </h2>
          <p className={cn(
            "text-lg leading-relaxed",
            isDark ? "text-gray-300" : "text-gray-600"
          )}>
            أن نكون المنصة الرائدة عالمياً في مجال الرعاية الصحية الرقمية، حيث يمكن لكل شخص الحصول على رعاية طبية عالية الجودة بسهولة وأمان.
          </p>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          className={cn(
            "p-8 rounded-3xl",
            isDark 
              ? "bg-gradient-to-br from-green-900/50 to-emerald-900/50 border border-green-700" 
              : "bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200"
          )}
        >
          <Heart className={cn(
            "w-12 h-12 mb-6",
            isDark ? "text-green-400" : "text-green-600"
          )} />
          <h2 className={cn(
            "text-2xl font-bold mb-4",
            isDark ? "text-white" : "text-gray-900"
          )}>
            مهمتنا
          </h2>
          <p className={cn(
            "text-lg leading-relaxed",
            isDark ? "text-gray-300" : "text-gray-600"
          )}>
            تمكين الأفراد من إدارة صحتهم بفعالية من خلال توفير أدوات ذكية وخدمات طبية متقدمة تجمع بين الخبرة البشرية والتكنولوجيا المبتكرة.
          </p>
        </motion.div>
      </motion.div>

      {/* Values */}
      <motion.div variants={staggerItem} className="mb-20">
        <div className="text-center mb-12">
          <h2 className={cn(
            "text-3xl font-bold mb-4",
            isDark ? "text-white" : "text-gray-900"
          )}>
            قيمنا الأساسية
          </h2>
          <p className={cn(
            "text-lg",
            isDark ? "text-gray-300" : "text-gray-600"
          )}>
            المبادئ التي توجه عملنا وتشكل هويتنا
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {values.map((value, index) => (
            <motion.div
              key={index}
              variants={staggerItem}
              whileHover={{ y: -10 }}
              className={cn(
                "p-6 rounded-2xl text-center",
                isDark ? "bg-gray-800/50 border border-gray-700" : "bg-white/50 border border-gray-200"
              )}
            >
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ delay: index * 0.1, type: "spring" }}
                className={cn(
                  "w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4",
                  `bg-gradient-to-r ${value.color}`
                )}
              >
                <value.icon className="w-8 h-8 text-white" />
              </motion.div>
              <h3 className={cn(
                "text-lg font-semibold mb-3",
                isDark ? "text-white" : "text-gray-900"
              )}>
                {value.title}
              </h3>
              <p className={cn(
                "text-sm",
                isDark ? "text-gray-300" : "text-gray-600"
              )}>
                {value.description}
              </p>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Team */}
      <motion.div variants={staggerItem} className="mb-20">
        <div className="text-center mb-12">
          <h2 className={cn(
            "text-3xl font-bold mb-4",
            isDark ? "text-white" : "text-gray-900"
          )}>
            فريقنا
          </h2>
          <p className={cn(
            "text-lg",
            isDark ? "text-gray-300" : "text-gray-600"
          )}>
            خبراء متخصصون يعملون لتحقيق رؤيتنا
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {team.map((member, index) => (
            <motion.div
              key={index}
              variants={staggerItem}
              whileHover={{ y: -10, scale: 1.02 }}
              className={cn(
                "p-6 rounded-2xl text-center",
                isDark ? "bg-gray-800/50 border border-gray-700" : "bg-white/50 border border-gray-200"
              )}
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: index * 0.2, type: "spring" }}
                className="relative mb-6"
              >
                <div className="w-24 h-24 rounded-full mx-auto overflow-hidden bg-gradient-to-r from-blue-500 to-purple-500 p-1">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full h-full rounded-full object-cover"
                  />
                </div>
                <motion.div
                  className="absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center"
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <Award className="w-4 h-4 text-white" />
                </motion.div>
              </motion.div>

              <h3 className={cn(
                "text-lg font-semibold mb-1",
                isDark ? "text-white" : "text-gray-900"
              )}>
                {member.name}
              </h3>
              
              <p className={cn(
                "text-sm font-medium mb-1",
                isDark ? "text-blue-400" : "text-blue-600"
              )}>
                {member.role}
              </p>
              
              <p className={cn(
                "text-xs mb-3 opacity-70",
                isDark ? "text-gray-400" : "text-gray-500"
              )}>
                {member.roleEn}
              </p>

              <p className={cn(
                "text-sm",
                isDark ? "text-gray-300" : "text-gray-600"
              )}>
                {member.bio}
              </p>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Testimonials */}
      <motion.div variants={staggerItem} className="mb-20">
        <div className="text-center mb-12">
          <h2 className={cn(
            "text-3xl font-bold mb-4",
            isDark ? "text-white" : "text-gray-900"
          )}>
            ماذا يقول عملاؤنا
          </h2>
          <p className={cn(
            "text-lg",
            isDark ? "text-gray-300" : "text-gray-600"
          )}>
            تجارب حقيقية من مستخدمي Si7ati
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              variants={staggerItem}
              whileHover={{ y: -5 }}
              className={cn(
                "p-6 rounded-2xl relative",
                isDark ? "bg-gray-800/50 border border-gray-700" : "bg-white/50 border border-gray-200"
              )}
            >
              <Quote className={cn(
                "w-8 h-8 mb-4 opacity-50",
                isDark ? "text-gray-400" : "text-gray-500"
              )} />
              
              <p className={cn(
                "text-sm leading-relaxed mb-4",
                isDark ? "text-gray-300" : "text-gray-600"
              )}>
                "{testimonial.content}"
              </p>

              <div className="flex items-center justify-between">
                <div>
                  <p className={cn(
                    "font-semibold",
                    isDark ? "text-white" : "text-gray-900"
                  )}>
                    {testimonial.name}
                  </p>
                  <p className={cn(
                    "text-xs",
                    isDark ? "text-gray-400" : "text-gray-500"
                  )}>
                    {testimonial.role}
                  </p>
                </div>
                
                <div className="flex">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* CTA */}
      <motion.div 
        variants={staggerItem}
        className="text-center"
      >
        <motion.div
          whileHover={{ scale: 1.02 }}
          className="p-12 rounded-3xl bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white"
        >
          <Zap className="w-16 h-16 mx-auto mb-6" />
          <h2 className="text-3xl font-bold mb-4">
            انضم إلى رحلة التطوير
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            كن جزءاً من مستقبل الرعاية الصحية الرقمية
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg"
            onClick={() => window.location.href = '/register'}
          >
            ابدأ معنا اليوم
          </motion.button>
        </motion.div>
      </motion.div>
    </motion.div>
  )
}

export default About
