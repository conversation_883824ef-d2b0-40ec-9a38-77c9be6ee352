{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh"}, "nav": {"home": "Home", "dashboard": "Dashboard", "aiConsultation": "AI Consultation", "appointments": "Appointments", "profile": "Profile", "features": "Features", "about": "About", "demo": "Try Demo", "login": "<PERSON><PERSON>", "register": "Sign Up", "logout": "Logout", "getStarted": "Get Started", "admin": "Admin Panel"}, "home": {"welcome": "Welcome to the future of healthcare", "title": "Welcome to", "subtitle": "AI-Powered Healthcare Platform connecting patients with specialists through artificial intelligence and video consultations", "startFree": "Start Free", "tryApp": "Try App", "goToDashboard": "Go to Dashboard", "whyChoose": "Why Choose", "discoverFuture": "Discover the future of healthcare with our comprehensive and advanced platform", "readyToTransform": "Ready to Transform Your Healthcare Experience?", "joinThousands": "Join thousands of patients and healthcare providers on Si7ati", "startJourney": "Start Your Journey Today", "demoCredentials": "Demo Credentials for Testing", "regularUser": "Regular User:", "adminUser": "System Administrator:", "username": "Username:", "password": "Password:"}, "features": {"title": "Si7ati Features", "subtitle": "Discover how Si7ati redefines digital healthcare through advanced technology and comprehensive medical services", "aiConsultation": {"title": "Advanced AI", "description": "Advanced AI system for symptom analysis and accurate preliminary diagnosis", "features": ["Symptom Analysis", "Smart Diagnosis", "Medical Advice", "Continuous Learning"]}, "videoConsultations": {"title": "Video Consultations", "description": "Connect with specialist doctors through secure, high-quality video calls", "features": ["HD Calls", "Instant Booking", "Certified Doctors", "Medical Records"]}, "security": {"title": "Security & Privacy", "description": "Advanced protection for your medical data with full encryption and international compliance", "features": ["Full Encryption", "HIPAA Compliant", "Complete Privacy", "Backups"]}, "available247": {"title": "24/7 Available", "description": "Medical service available 24/7 to ensure you get help when you need it", "features": ["Continuous Service", "Quick Response", "Emergency", "Instant Support"]}, "medicalTeam": {"title": "Specialized Medical Team", "description": "Network of certified doctors and specialists in various medical fields", "features": ["Certified Doctors", "Diverse Specialties", "High Experience", "Patient Reviews"]}, "easyToUse": {"title": "Easy to Use", "description": "Simple and easy-to-use interface suitable for all ages and technical levels", "features": ["Intuitive Interface", "Responsive Design", "Multi-language Support", "Clear Instructions"]}, "stats": {"activeUsers": "Active Users", "certifiedDoctors": "Certified Doctors", "uptime": "Uptime", "userRating": "User Rating"}, "advancedTechnology": "Advanced Technology", "technologySubtitle": "We use the latest technologies to ensure the best digital medical experience", "artificialIntelligence": "Artificial Intelligence", "aiDescription": "Advanced algorithms for diagnosis", "advancedSecurity": "Advanced Security", "securityDescription": "Military-grade encryption", "fastPerformance": "Fast Performance", "performanceDescription": "Instant response and high reliability", "readyForFuture": "Ready to Experience the Future?", "joinUsers": "Join thousands of users who trust Si7ati for their healthcare"}, "about": {"title": "About Us", "subtitle": "Si7ati is a leading platform in digital healthcare, aiming to make medical services more accessible to everyone through advanced technology and artificial intelligence", "globalService": "Global service - Local care", "ourVision": "Our Vision", "visionText": "To be the world's leading platform in digital healthcare, where everyone can access high-quality medical care easily and safely.", "ourMission": "Our Mission", "missionText": "Empowering individuals to manage their health effectively by providing smart tools and advanced medical services that combine human expertise with innovative technology.", "coreValues": "Our Core Values", "valuesSubtitle": "The principles that guide our work and shape our identity", "careFirst": "Care First", "careDescription": "We always put patient health and well-being first", "trustSecurity": "Trust & Security", "trustDescription": "Protecting your medical data with the highest security standards", "continuousInnovation": "Continuous Innovation", "innovationDescription": "We develop technology to improve medical services", "collaboration": "Collaboration", "collaborationDescription": "We work with the best doctors and specialists", "ourTeam": "Our Team", "teamSubtitle": "Specialized experts working to achieve our vision", "chiefMedicalOfficer": "Chief Medical Officer", "chiefTechnologyOfficer": "Chief Technology Officer", "productManager": "Product Manager", "testimonials": "What Our Customers Say", "testimonialsSubtitle": "Real experiences from Si7ati users", "joinDevelopment": "Join the Development Journey", "bePartOfFuture": "Be part of the future of digital healthcare", "startWithUs": "Start with us today"}, "demo": {"title": "Si7ati AI Demo", "subtitle": "Try <PERSON><PERSON><PERSON>'s smart assistant for free", "freeTrialNoRegistration": "Free trial - No registration required", "welcomeMessage": "Hello! I'm <PERSON><PERSON><PERSON>'s smart assistant. I can help you analyze symptoms and provide initial medical advice. How can I help you today?", "typeSymptoms": "Type your symptoms here...", "commonSymptoms": "Common Symptoms", "importantNote": "Important Note", "demoLimitation": "This is a simplified demo version. For accurate and advanced diagnosis, please register with Si7ati.", "readyToStart": "Ready to Start?", "getAdvanced": "Get advanced diagnosis and comprehensive medical services", "createFreeAccount": "Create Free Account", "connectedNow": "Connected now", "send": "Send"}, "auth": {"signInToAccount": "Sign in to your account", "createAccount": "Create Account", "orCreateNew": "Or create a new account", "orSignInExisting": "Or sign in to your existing account", "fullName": "Full Name", "email": "Email Address", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "accountType": "Account Type", "patient": "Patient", "doctor": "Doctor", "phoneNumber": "Phone Number", "gender": "Gender", "selectGender": "Select gender", "male": "Male", "female": "Female", "other": "Other", "signIn": "Sign In", "enterFullName": "Enter your full name", "enterEmail": "Enter your email", "chooseUsername": "Choose a username", "enterPassword": "Enter your password", "confirmYourPassword": "Confirm your password", "demoCredentials": "Demo Credentials:", "loginSuccessful": "Login successful!", "registrationSuccessful": "Registration successful! Please login.", "loginFailed": "<PERSON><PERSON> failed", "registrationFailed": "Registration failed", "passwordsDontMatch": "Passwords do not match"}, "dashboard": {"welcomeBack": "Welcome back", "overviewToday": "Here's what you can do with Si7ati today", "aiConsultation": "AI Consultation", "aiDescription": "Get instant medical advice based on your symptoms", "bookAppointment": "Book Appointment", "appointmentDescription": "Schedule a video consultation with a doctor", "viewProfile": "View Profile", "profileDescription": "Update your personal and medical information", "aiConsultations": "AI Consultations", "appointments": "Appointments", "upcoming": "Upcoming", "records": "Records", "recentActivity": "Recent Activity", "noRecentActivity": "No recent activity", "startByGetting": "Start by getting an AI consultation or booking an appointment"}, "admin": {"title": "Admin Dashboard", "welcomeAdmin": "Welcome {{name}}, here's an overview of the platform", "systemRunning": "System running normally", "demoDataNotice": "Demo data for administration", "demoDataDescription": "The information displayed here is demo data for demonstration purposes", "overview": "Overview", "users": "Users", "consultations": "Consultations", "settings": "Settings", "totalUsers": "Total Users", "consultationsToday": "Consultations Today", "scheduledAppointments": "Scheduled Appointments", "monthlyRevenue": "Monthly Revenue", "recentActivities": "Recent Activities", "topDoctors": "Top Doctors", "userManagement": "User Management", "userManagementDescription": "This section is under development - user management will be added soon", "consultationManagement": "Consultation Management", "consultationManagementDescription": "This section is under development - consultation management will be added soon", "systemSettings": "System Settings", "systemSettingsDescription": "This section is under development - system settings will be added soon"}, "consultation": {"title": "AI Medical Consultation", "subtitle": "Get instant medical advice based on your symptoms. This is not a replacement for professional medical care.", "tellUsSymptoms": "Tell us about your symptoms", "age": "Age", "enterAge": "Enter your age", "gender": "Gender", "symptoms": "Symptoms", "describeSymptoms": "Describe your symptoms separated by commas (e.g., headache, nausea, fever)", "separateSymptoms": "Separate multiple symptoms with commas", "getConsultation": "Get AI Consultation", "analyzing": "Analyzing...", "consultationResults": "Consultation Results", "urgencyLevel": "Urgency Level", "medicalAdvice": "Medical Advice", "suggestedAction": "Suggested Action", "confidenceScore": "Confidence Score", "newConsultation": "New Consultation", "bookAppointment": "Book Appointment", "noConsultationYet": "No consultation yet", "fillFormForConsultation": "Fill out the form to get your AI-powered medical consultation", "importantDisclaimer": "Important Disclaimer", "disclaimerText": "This AI consultation is for informational purposes only and should not replace professional medical advice. Always consult with a qualified healthcare provider for serious symptoms.", "high": "High", "medium": "Medium", "low": "Low"}, "appointments": {"title": "Appointments", "subtitle": "Manage your appointments and schedule new consultations", "comingSoon": "Appointments Coming Soon", "workingOnSystem": "We're working on the appointment booking system. For now, you can use our AI consultation feature.", "tryAiConsultation": "Try AI Consultation"}, "profile": {"title": "Profile", "subtitle": "Manage your personal information and account settings", "personalInformation": "Personal Information", "accountInformation": "Account Information", "fullName": "Full Name", "email": "Email", "username": "Username", "phoneNumber": "Phone Number", "gender": "Gender", "accountType": "Account Type", "subscriptionPlan": "Subscription Plan", "accountStatus": "Account Status", "emailVerified": "<PERSON><PERSON>", "active": "Active", "inactive": "Inactive", "verified": "Verified", "pending": "Pending", "notProvided": "Not provided", "free": "Free", "editProfile": "Edit Profile"}, "subscription": {"title": "Choose Your Plan", "subtitle": "Select the perfect plan for your healthcare needs. Upgrade or downgrade at any time.", "currentPlan": "Current Plan", "upgradeTo": "Upgrade to {{plan}}", "free": "Free", "basic": "Basic", "premium": "Premium", "month": "month", "forever": "forever", "freeFeatures": ["Basic AI consultations", "Limited appointments", "Basic health tracking", "Email support"], "basicFeatures": ["Unlimited AI consultations", "Video appointments", "Advanced health tracking", "Priority support", "Medical records storage"], "premiumFeatures": ["Everything in Basic", "Specialist consultations", "Family account (up to 4 members)", "24/7 emergency support", "Prescription management", "Health insights & analytics"], "billingInformation": "Billing Information", "currentSubscription": "Current Subscription", "currentlyOn": "You are currently on the {{plan}} plan.", "paymentMethod": "Payment Method", "noPaymentMethod": "No payment method on file. Add a payment method to upgrade your plan.", "nextBillingDate": "Next Billing Date", "noBillingRequired": "No billing required", "notAvailableDemo": "Not available in demo", "billingHistory": "Billing History", "noBillingHistory": "No billing history available.", "faq": "Frequently Asked Questions", "canChangePlan": "Can I change my plan anytime?", "canChangePlanAnswer": "Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.", "isThereFreeTrial": "Is there a free trial?", "freeTrialAnswer": "All new users start with our Free plan. You can upgrade to paid plans when you need additional features.", "paymentMethods": "What payment methods do you accept?", "paymentMethodsAnswer": "We accept all major credit cards, debit cards, and digital wallets through our secure payment processor.", "canCancelAnytime": "Can I cancel anytime?", "cancelAnytimeAnswer": "Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your billing period."}}