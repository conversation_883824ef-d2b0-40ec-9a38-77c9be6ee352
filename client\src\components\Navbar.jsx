import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import { Moon, Sun, Menu, X, Heart, Stethoscope } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'
import { useTheme } from '../contexts/ThemeContext'
import { useLanguage } from '../contexts/LanguageContext'
import LanguageSwitcher from './LanguageSwitcher'
import { cn } from '../utils/cn'

const Navbar = () => {
  const { user, logout, isAuthenticated } = useAuth()
  const { isDark, toggleTheme } = useTheme()
  const { isRTL } = useLanguage()
  const { t } = useTranslation()
  const navigate = useNavigate()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const handleLogout = () => {
    logout()
    navigate('/')
  }

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={cn(
        "sticky top-0 z-50 backdrop-blur-md border-b transition-all duration-300",
        isDark
          ? "bg-gray-900/80 border-gray-700"
          : "bg-white/80 border-gray-200"
      )}
    >
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <motion.div
            className="flex items-center"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link to="/" className="flex items-center space-x-3">
              <div className={cn(
                "w-10 h-10 rounded-xl flex items-center justify-center relative overflow-hidden",
                "bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500",
                "shadow-lg"
              )}>
                <Stethoscope className="w-6 h-6 text-white" />
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                  animate={{ x: [-100, 100] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                />
              </div>
              <div className="flex flex-col">
                <span className={cn(
                  "text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",
                  isDark && "from-blue-400 to-purple-400"
                )}>
                  Si7ati
                </span>
                <span className={cn(
                  "text-xs font-medium",
                  isDark ? "text-gray-400" : "text-gray-500"
                )}>
                  صحتي
                </span>
              </div>
            </Link>
          </motion.div>

          {/* Desktop Navigation */}
          <div className={cn(
            "hidden md:flex items-center space-x-6",
            isRTL && "space-x-reverse"
          )}>
            {isAuthenticated ? (
              <>
                <NavLink to="/dashboard" isDark={isDark}>{t('nav.dashboard')}</NavLink>
                <NavLink to="/ai-consultation" isDark={isDark}>{t('nav.aiConsultation')}</NavLink>
                <NavLink to="/appointments" isDark={isDark}>{t('nav.appointments')}</NavLink>
                <NavLink to="/profile" isDark={isDark}>{t('nav.profile')}</NavLink>

                <div className={cn(
                  "flex items-center space-x-4 ml-4 pl-4 border-l border-gray-300 dark:border-gray-600",
                  isRTL && "space-x-reverse mr-4 pr-4 ml-0 pl-0 border-l-0 border-r"
                )}>
                  <LanguageSwitcher />

                  <motion.button
                    onClick={toggleTheme}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className={cn(
                      "p-2 rounded-lg transition-colors",
                      isDark
                        ? "bg-gray-800 text-yellow-400 hover:bg-gray-700"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    )}
                  >
                    {isDark ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
                  </motion.button>

                  <div className={cn(
                    "flex items-center space-x-3",
                    isRTL && "space-x-reverse"
                  )}>
                    <div className={cn(
                      "flex items-center space-x-2",
                      isRTL && "space-x-reverse"
                    )}>
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {user?.full_name?.charAt(0)?.toUpperCase()}
                        </span>
                      </div>
                      <span className={cn(
                        "text-sm font-medium",
                        isDark ? "text-gray-300" : "text-gray-700"
                      )}>
                        {user?.full_name}
                      </span>
                    </div>
                    <motion.button
                      onClick={handleLogout}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg"
                    >
                      {t('nav.logout')}
                    </motion.button>
                  </div>
                </div>
              </>
            ) : (
              <>
                <NavLink to="/demo" isDark={isDark}>{t('nav.demo')}</NavLink>
                <NavLink to="/features" isDark={isDark}>{t('nav.features')}</NavLink>
                <NavLink to="/about" isDark={isDark}>{t('nav.about')}</NavLink>

                <LanguageSwitcher />

                <motion.button
                  onClick={toggleTheme}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className={cn(
                    "p-2 rounded-lg transition-colors",
                    isDark
                      ? "bg-gray-800 text-yellow-400 hover:bg-gray-700"
                      : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                  )}
                >
                  {isDark ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
                </motion.button>

                <div className={cn(
                  "flex items-center space-x-3",
                  isRTL && "space-x-reverse"
                )}>
                  <Link
                    to="/login"
                    className={cn(
                      "px-4 py-2 rounded-lg text-sm font-medium transition-colors",
                      isDark
                        ? "text-gray-300 hover:text-white hover:bg-gray-800"
                        : "text-gray-700 hover:text-gray-900 hover:bg-gray-100"
                    )}
                  >
                    {t('nav.login')}
                  </Link>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Link
                      to="/register"
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-lg text-sm font-medium shadow-lg"
                    >
                      {t('nav.getStarted')}
                    </Link>
                  </motion.div>
                </div>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-2">
            <motion.button
              onClick={toggleTheme}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className={cn(
                "p-2 rounded-lg transition-colors",
                isDark
                  ? "bg-gray-800 text-yellow-400 hover:bg-gray-700"
                  : "bg-gray-100 text-gray-600 hover:bg-gray-200"
              )}
            >
              {isDark ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
            </motion.button>

            <motion.button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className={cn(
                "p-2 rounded-lg transition-colors",
                isDark
                  ? "text-gray-300 hover:text-white hover:bg-gray-800"
                  : "text-gray-700 hover:text-gray-900 hover:bg-gray-100"
              )}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </motion.button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden overflow-hidden"
            >
              <div className={cn(
                "px-4 pt-2 pb-3 space-y-1 border-t",
                isDark ? "border-gray-700 bg-gray-900/50" : "border-gray-200 bg-white/50"
              )}>
                {isAuthenticated ? (
                  <>
                    <MobileNavLink to="/dashboard" onClick={() => setIsMenuOpen(false)} isDark={isDark}>
                      Dashboard
                    </MobileNavLink>
                    <MobileNavLink to="/ai-consultation" onClick={() => setIsMenuOpen(false)} isDark={isDark}>
                      AI Consultation
                    </MobileNavLink>
                    <MobileNavLink to="/appointments" onClick={() => setIsMenuOpen(false)} isDark={isDark}>
                      Appointments
                    </MobileNavLink>
                    <MobileNavLink to="/profile" onClick={() => setIsMenuOpen(false)} isDark={isDark}>
                      Profile
                    </MobileNavLink>

                    <div className="pt-4 border-t border-gray-300 dark:border-gray-600">
                      <div className="flex items-center space-x-3 px-3 py-2">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-sm font-medium">
                            {user?.full_name?.charAt(0)?.toUpperCase()}
                          </span>
                        </div>
                        <span className={cn(
                          "text-sm font-medium",
                          isDark ? "text-gray-300" : "text-gray-700"
                        )}>
                          {user?.full_name}
                        </span>
                      </div>
                      <motion.button
                        onClick={handleLogout}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="w-full text-left px-3 py-2 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md text-base font-medium"
                      >
                        Logout
                      </motion.button>
                    </div>
                  </>
                ) : (
                  <>
                    <MobileNavLink to="/demo" onClick={() => setIsMenuOpen(false)} isDark={isDark}>
                      Try Demo
                    </MobileNavLink>
                    <MobileNavLink to="/features" onClick={() => setIsMenuOpen(false)} isDark={isDark}>
                      Features
                    </MobileNavLink>
                    <MobileNavLink to="/about" onClick={() => setIsMenuOpen(false)} isDark={isDark}>
                      About
                    </MobileNavLink>

                    <div className="pt-4 space-y-2">
                      <Link
                        to="/login"
                        className={cn(
                          "block px-3 py-2 rounded-md text-base font-medium transition-colors",
                          isDark
                            ? "text-gray-300 hover:text-white hover:bg-gray-800"
                            : "text-gray-700 hover:text-gray-900 hover:bg-gray-100"
                        )}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Login
                      </Link>
                      <Link
                        to="/register"
                        className="block bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-2 rounded-md text-base font-medium"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Get Started
                      </Link>
                    </div>
                  </>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.nav>
  )
}

// NavLink Component
const NavLink = ({ to, children, isDark }) => (
  <motion.div whileHover={{ y: -2 }} whileTap={{ y: 0 }}>
    <Link
      to={to}
      className={cn(
        "px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 relative group",
        isDark
          ? "text-gray-300 hover:text-white hover:bg-gray-800"
          : "text-gray-700 hover:text-gray-900 hover:bg-gray-100"
      )}
    >
      {children}
      <motion.div
        className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
        initial={{ scaleX: 0 }}
        whileHover={{ scaleX: 1 }}
        transition={{ duration: 0.2 }}
      />
    </Link>
  </motion.div>
)

// Mobile NavLink Component
const MobileNavLink = ({ to, children, onClick, isDark }) => (
  <motion.div whileHover={{ x: 4 }} whileTap={{ x: 0 }}>
    <Link
      to={to}
      onClick={onClick}
      className={cn(
        "block px-3 py-2 rounded-md text-base font-medium transition-colors",
        isDark
          ? "text-gray-300 hover:text-white hover:bg-gray-800"
          : "text-gray-700 hover:text-gray-900 hover:bg-gray-100"
      )}
    >
      {children}
    </Link>
  </motion.div>
)

export default Navbar
