import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON>, Send, <PERSON>rkles, Heart, AlertCircle, CheckCircle } from 'lucide-react'
import { useTheme } from '../contexts/ThemeContext'
import { cn } from '../utils/cn'
import { fadeInUp, staggerContainer, staggerItem } from '../utils/animations'
import axios from 'axios'
import toast from 'react-hot-toast'

const Demo = () => {
  const { isDark } = useTheme()
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'bot',
      content: 'مرحباً! أنا مساعد Si7ati الذكي. يمكنني مساعدتك في تحليل الأعراض وتقديم النصائح الطبية الأولية. كيف يمكنني مساعدتك اليوم؟',
      timestamp: new Date()
    }
  ])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [demoData, setDemoData] = useState({
    age: '',
    gender: '',
    symptoms: ''
  })

  const handleQuickSymptom = (symptom) => {
    setInput(symptom)
  }

  const handleSendMessage = async () => {
    if (!input.trim()) return

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: input,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)

    try {
      // Simulate AI response for demo
      setTimeout(() => {
        const botResponse = {
          id: Date.now() + 1,
          type: 'bot',
          content: generateDemoResponse(input),
          timestamp: new Date()
        }
        setMessages(prev => [...prev, botResponse])
        setIsLoading(false)
      }, 1500)
    } catch (error) {
      toast.error('حدث خطأ في الاتصال')
      setIsLoading(false)
    }

    setInput('')
  }

  const generateDemoResponse = (symptom) => {
    const responses = {
      'صداع': 'بناءً على وصفك للصداع، قد يكون السبب التوتر أو قلة النوم. أنصحك بالراحة وشرب الماء والابتعاد عن الضوضاء. إذا استمر الصداع أكثر من يومين، يُفضل استشارة طبيب.',
      'حمى': 'الحمى قد تكون علامة على عدوى. أنصحك بالراحة وشرب السوائل وخفض درجة الحرارة. إذا تجاوزت الحرارة 39 درجة أو استمرت أكثر من 3 أيام، يجب مراجعة الطبيب فوراً.',
      'سعال': 'السعال قد يكون بسبب نزلة برد أو حساسية. أنصحك بشرب السوائل الدافئة والعسل. إذا كان مصحوباً بدم أو استمر أكثر من أسبوعين، راجع الطبيب.',
      'headache': 'Based on your headache symptoms, it could be due to stress or lack of sleep. I recommend rest, hydration, and avoiding loud noises. If it persists for more than 2 days, please consult a doctor.',
      'fever': 'Fever may indicate an infection. Rest, drink fluids, and monitor your temperature. If it exceeds 39°C or lasts more than 3 days, seek immediate medical attention.',
      'cough': 'Cough could be due to cold or allergies. Try warm liquids and honey. If accompanied by blood or lasts more than 2 weeks, see a doctor.'
    }

    const lowerInput = symptom.toLowerCase()
    for (const [key, response] of Object.entries(responses)) {
      if (lowerInput.includes(key)) {
        return response
      }
    }

    return 'شكراً لك على وصف الأعراض. للحصول على تحليل دقيق أكثر، أنصحك بالتسجيل في Si7ati واستخدام نظام التشخيص المتقدم. يمكنني تقديم نصائح عامة، لكن التشخيص الدقيق يتطلب فحص طبي مناسب.'
  }

  const quickSymptoms = [
    'صداع', 'حمى', 'سعال', 'ألم في المعدة', 'دوخة', 'إرهاق',
    'headache', 'fever', 'cough', 'stomach pain', 'dizziness', 'fatigue'
  ]

  return (
    <motion.div
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      className="max-w-6xl mx-auto"
    >
      {/* Header */}
      <motion.div variants={staggerItem} className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-4"
          >
            <Bot className="w-8 h-8 text-white" />
          </motion.div>
          <div>
            <h1 className={cn(
              "text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",
              isDark && "from-blue-400 to-purple-400"
            )}>
              Si7ati AI Demo
            </h1>
            <p className={cn(
              "text-lg mt-2",
              isDark ? "text-gray-300" : "text-gray-600"
            )}>
              جرب مساعد Si7ati الذكي مجاناً
            </p>
          </div>
        </div>
        
        <motion.div
          variants={fadeInUp}
          className={cn(
            "inline-flex items-center px-4 py-2 rounded-full text-sm font-medium",
            isDark 
              ? "bg-blue-900/30 text-blue-300 border border-blue-700" 
              : "bg-blue-100 text-blue-700 border border-blue-200"
          )}
        >
          <Sparkles className="w-4 h-4 mr-2" />
          تجربة مجانية - لا حاجة للتسجيل
        </motion.div>
      </motion.div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Chat Interface */}
        <motion.div variants={staggerItem} className="lg:col-span-2">
          <div className={cn(
            "rounded-2xl shadow-xl overflow-hidden",
            isDark ? "bg-gray-800 border border-gray-700" : "bg-white border border-gray-200"
          )}>
            {/* Chat Header */}
            <div className={cn(
              "px-6 py-4 border-b",
              isDark ? "bg-gray-900 border-gray-700" : "bg-gray-50 border-gray-200"
            )}>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                  <Heart className="w-5 h-5 text-white" />
                </div>
                <div className="ml-3">
                  <h3 className={cn(
                    "font-semibold",
                    isDark ? "text-white" : "text-gray-900"
                  )}>
                    Si7ati Assistant
                  </h3>
                  <p className={cn(
                    "text-sm",
                    isDark ? "text-gray-400" : "text-gray-500"
                  )}>
                    متصل الآن
                  </p>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="h-96 overflow-y-auto p-6 space-y-4">
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={cn(
                    "flex",
                    message.type === 'user' ? "justify-end" : "justify-start"
                  )}
                >
                  <div className={cn(
                    "max-w-xs lg:max-w-md px-4 py-3 rounded-2xl",
                    message.type === 'user'
                      ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white"
                      : isDark
                        ? "bg-gray-700 text-gray-100"
                        : "bg-gray-100 text-gray-900"
                  )}>
                    <p className="text-sm leading-relaxed">{message.content}</p>
                    <p className={cn(
                      "text-xs mt-2 opacity-70",
                      message.type === 'user' ? "text-blue-100" : isDark ? "text-gray-400" : "text-gray-500"
                    )}>
                      {message.timestamp.toLocaleTimeString('ar-SA', { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </p>
                  </div>
                </motion.div>
              ))}
              
              {isLoading && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex justify-start"
                >
                  <div className={cn(
                    "px-4 py-3 rounded-2xl",
                    isDark ? "bg-gray-700" : "bg-gray-100"
                  )}>
                    <div className="flex space-x-1">
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1, repeat: Infinity, delay: 0 }}
                        className={cn(
                          "w-2 h-2 rounded-full",
                          isDark ? "bg-gray-400" : "bg-gray-500"
                        )}
                      />
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
                        className={cn(
                          "w-2 h-2 rounded-full",
                          isDark ? "bg-gray-400" : "bg-gray-500"
                        )}
                      />
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
                        className={cn(
                          "w-2 h-2 rounded-full",
                          isDark ? "bg-gray-400" : "bg-gray-500"
                        )}
                      />
                    </div>
                  </div>
                </motion.div>
              )}
            </div>

            {/* Input */}
            <div className={cn(
              "px-6 py-4 border-t",
              isDark ? "border-gray-700" : "border-gray-200"
            )}>
              <div className="flex space-x-4">
                <input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="اكتب أعراضك هنا..."
                  className={cn(
                    "flex-1 px-4 py-3 rounded-xl border focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all",
                    isDark 
                      ? "bg-gray-700 border-gray-600 text-white placeholder-gray-400" 
                      : "bg-white border-gray-300 text-gray-900 placeholder-gray-500"
                  )}
                />
                <motion.button
                  onClick={handleSendMessage}
                  disabled={!input.trim() || isLoading}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  <Send className="w-5 h-5" />
                </motion.button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Quick Actions & Info */}
        <motion.div variants={staggerItem} className="space-y-6">
          {/* Quick Symptoms */}
          <div className={cn(
            "p-6 rounded-2xl",
            isDark ? "bg-gray-800 border border-gray-700" : "bg-white border border-gray-200"
          )}>
            <h3 className={cn(
              "font-semibold mb-4",
              isDark ? "text-white" : "text-gray-900"
            )}>
              أعراض شائعة
            </h3>
            <div className="grid grid-cols-2 gap-2">
              {quickSymptoms.map((symptom, index) => (
                <motion.button
                  key={index}
                  onClick={() => handleQuickSymptom(symptom)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={cn(
                    "p-3 text-sm rounded-lg border transition-all",
                    isDark 
                      ? "bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600" 
                      : "bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100"
                  )}
                >
                  {symptom}
                </motion.button>
              ))}
            </div>
          </div>

          {/* Demo Limitations */}
          <div className={cn(
            "p-6 rounded-2xl",
            isDark ? "bg-yellow-900/20 border border-yellow-700" : "bg-yellow-50 border border-yellow-200"
          )}>
            <div className="flex items-start">
              <AlertCircle className={cn(
                "w-5 h-5 mt-0.5 mr-3",
                isDark ? "text-yellow-400" : "text-yellow-600"
              )} />
              <div>
                <h4 className={cn(
                  "font-semibold mb-2",
                  isDark ? "text-yellow-400" : "text-yellow-800"
                )}>
                  ملاحظة مهمة
                </h4>
                <p className={cn(
                  "text-sm",
                  isDark ? "text-yellow-300" : "text-yellow-700"
                )}>
                  هذه نسخة تجريبية مبسطة. للحصول على تشخيص دقيق ومتقدم، يرجى التسجيل في Si7ati.
                </p>
              </div>
            </div>
          </div>

          {/* CTA */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            className={cn(
              "p-6 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-500 text-white"
            )}
          >
            <CheckCircle className="w-8 h-8 mb-3" />
            <h4 className="font-semibold mb-2">جاهز للبدء؟</h4>
            <p className="text-sm text-blue-100 mb-4">
              احصل على تشخيص متقدم وخدمات طبية شاملة
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-full bg-white text-blue-600 py-3 rounded-lg font-medium"
              onClick={() => window.location.href = '/register'}
            >
              إنشاء حساب مجاني
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </motion.div>
  )
}

export default Demo
