"""
Simple API test script
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_endpoints():
    """Test basic API endpoints"""
    
    print("Testing HealthSync API...")
    
    # Test root endpoint
    response = requests.get(f"{BASE_URL}/")
    print(f"Root endpoint: {response.status_code} - {response.json()}")
    
    # Test health check
    response = requests.get(f"{BASE_URL}/health")
    print(f"Health check: {response.status_code} - {response.json()}")
    
    # Test AI consultation demo
    response = requests.get(f"{BASE_URL}/api/v1/ai/consult/demo")
    print(f"AI Demo: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"  Advice: {result['advice'][:50]}...")
        print(f"  Urgency: {result['urgency']}")
    
    # Test user registration
    user_data = {
        "email": "<EMAIL>",
        "username": "testuser",
        "full_name": "Test User",
        "password": "testpassword123",
        "role": "patient"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/register", json=user_data)
    print(f"User registration: {response.status_code}")
    if response.status_code == 200:
        print("  User created successfully")
    elif response.status_code == 400:
        print("  User already exists (expected)")
    
    # Test user login
    login_data = {
        "username": "testuser",
        "password": "testpassword123"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login-json", json=login_data)
    print(f"User login: {response.status_code}")
    if response.status_code == 200:
        token = response.json()["access_token"]
        print("  Login successful, token received")
        
        # Test protected endpoint
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/api/v1/users/me", headers=headers)
        print(f"Protected endpoint: {response.status_code}")
        if response.status_code == 200:
            user = response.json()
            print(f"  User: {user['full_name']} ({user['email']})")

if __name__ == "__main__":
    test_endpoints()
