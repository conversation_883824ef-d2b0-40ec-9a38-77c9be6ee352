"""
Medical Record model for the HealthSync application
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from .database import Base

class MedicalRecord(Base):
    __tablename__ = "medical_records"

    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign key
    patient_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Medical information
    chief_complaint = Column(Text, nullable=True)
    symptoms = Column(JSON, nullable=True)  # List of symptoms
    diagnosis = Column(Text, nullable=True)
    treatment_plan = Column(Text, nullable=True)
    medications = Column(JSON, nullable=True)  # List of medications
    
    # Vital signs
    blood_pressure = Column(String, nullable=True)
    heart_rate = Column(Integer, nullable=True)
    temperature = Column(String, nullable=True)
    weight = Column(String, nullable=True)
    height = Column(String, nullable=True)
    
    # Medical history
    allergies = Column(JSON, nullable=True)  # List of allergies
    chronic_conditions = Column(JSON, nullable=True)  # List of chronic conditions
    family_history = Column(Text, nullable=True)
    surgical_history = Column(Text, nullable=True)
    
    # Additional notes
    notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships will be defined after all models are imported

class AIConsultation(Base):
    __tablename__ = "ai_consultations"

    id = Column(Integer, primary_key=True, index=True)
    
    # Foreign key
    patient_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Input data
    age = Column(Integer, nullable=False)
    gender = Column(String, nullable=False)
    symptoms = Column(JSON, nullable=False)  # List of symptoms
    
    # AI response
    advice = Column(Text, nullable=False)
    urgency = Column(String, nullable=False)  # Low, Medium, High
    suggested_action = Column(Text, nullable=False)
    confidence_score = Column(String, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships will be defined after all models are imported
